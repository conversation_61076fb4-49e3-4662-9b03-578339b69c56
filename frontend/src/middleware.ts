// frontend/src/middleware.ts
import { NextResponse, type NextRequest } from "next/server";
import { UserRole } from "@/lib/types/auth";
import { parseJwtPayload } from "@/lib/supabase/client";
import { getUnifiedSession, getUserRoles } from "@/lib/auth/getUnifiedSession";
import { isSuperAdminEmail } from "@/lib/auth/constants";

// Define protected routes and their required roles (using the standardized UserRole type)
const protectedRoutes: Record<string, UserRole[]> = {
  "/admin": [UserRole.Partner], // Regular admin dashboard (only partners)
  "/dashboard": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ], // Staff dashboard
  "/client-portal": [UserRole.Client], // Client portal
  "/cases": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ],
  "/matters": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ],
  "/documents": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ],
  "/settings": [UserRole.Admin], // Tenant settings (Tenant Admins only)
  "/billing": [UserRole.Partner], // SaaS Billing (Partners only)
  "/users": [UserRole.Admin], // Tenant user management (Tenant Admins only)
  "/calendar": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ],
  "/tasks": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ],
  "/clients": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ],
  "/submit-case": [UserRole.Client],
  // Note: /superadmin is handled separately by JWT is_super_admin claim
};

export async function middleware(request: NextRequest): Promise<NextResponse> {
  // CRITICAL: This should appear in browser console for EVERY request
  console.log("🚀🚀🚀 MIDDLEWARE IS RUNNING! 🚀🚀🚀", {
    path: request.nextUrl.pathname,
    url: request.url,
    timestamp: new Date().toISOString(),
    userAgent: request.headers.get('user-agent')?.substring(0, 50)
  });

  const response = NextResponse.next({ request });

  // Get unified session (supports both Supabase and legacy auth)
  const session = await getUnifiedSession(request.cookies);
  const user = session?.user || null;
  console.log(
    "Middleware: User object from supabase.auth.getUser():",
    JSON.stringify(user, null, 2),
  );
  console.log(
    "Middleware: Extracted user_metadata:",
    JSON.stringify(user?.user_metadata, null, 2),
  );
  const currentPath = request.nextUrl.pathname;

  // --- Authentication Check ---
  const isProtectedRoute = Object.keys(protectedRoutes).some((route) =>
    currentPath.startsWith(route),
  );
  const isSuperAdminRoute = currentPath.startsWith("/superadmin");

  if (!user && (isProtectedRoute || isSuperAdminRoute)) {
    const loginUrl = isSuperAdminRoute ? "/loginadmin" : "/login";
    const redirectUrl = new URL(loginUrl, request.url);
    redirectUrl.searchParams.set("redirectedFrom", currentPath);
    console.log(
      `Middleware: Unauthenticated access to ${currentPath}. Redirecting to ${loginUrl}.`,
    );
    return NextResponse.redirect(redirectUrl);
  }

  // --- Authorization Check (only if user is authenticated) ---
  if (user) {
    const userEmail = user.email;

    // CHECK SUPERADMIN STATUS FIRST (before any role checks)
    let isSuperAdmin = false;

    // Primary check: centralized superadmin email list
    if (isSuperAdminEmail(userEmail)) {
      isSuperAdmin = true;
    }

    // Secondary check: JWT claim (if available)
    if (!isSuperAdmin && session?.access_token) {
      const jwtPayload = parseJwtPayload(session.access_token);
      isSuperAdmin = jwtPayload?.is_super_admin === true;
    }

    // Tertiary check: user_metadata (fallback)
    if (!isSuperAdmin && user.user_metadata?.is_super_admin === true) {
      isSuperAdmin = true;
    }

    // 1. Superadmin Route Check (by JWT is_super_admin claim)
    if (isSuperAdminRoute) {
      if (!isSuperAdmin) {
        console.warn(
          `Middleware: Unauthorized attempt on /superadmin by ${userEmail}. User is not a super admin. Redirecting.`,
        );
        return NextResponse.redirect(
          new URL("/dashboard?error=superadmin_unauthorized", request.url),
        );
      }

      console.log(`Middleware: Super admin access granted to ${userEmail} (source: ${session.source})`);
      // Allow superadmin access
      return response;
    }

    // 2. SUPERADMIN BYPASS: If user is superadmin, allow access to all routes
    if (isSuperAdmin) {
      console.log(`Middleware: Superadmin ${userEmail} granted access to ${currentPath} via superadmin bypass`);
      return response;
    }

    // 3. Regular Protected Routes Check (by role) - only for non-superadmin users
    // Get user roles from unified session
    const userRoles = getUserRoles(session);

    // ENHANCED DEBUG LOGGING
    console.log("🔍 MIDDLEWARE DEBUG:", {
      currentPath,
      userEmail,
      userId: user?.id,
      userRoles,
      userRolesLength: userRoles.length,
      sessionSource: session?.source,
      appMetadata: user?.app_metadata,
      userMetadata: user?.user_metadata,
      hasSession: !!session,
      hasUser: !!user
    });

    // Check if user has valid session
    if (!user?.id) {
      console.log("Middleware: User ID missing, redirecting to login");
      const loginUrl = "/login";
      const redirectUrl = new URL(loginUrl, request.url);
      redirectUrl.searchParams.set("redirectedFrom", currentPath);
      return NextResponse.redirect(redirectUrl);
    }

    // If user has no roles, redirect to login (superadmins already handled above)
    if (userRoles.length === 0) {
      console.log("Middleware: User roles missing, redirecting to login");
      const loginUrl = "/login";
      const redirectUrl = new URL(loginUrl, request.url);
      redirectUrl.searchParams.set("redirectedFrom", currentPath);
      return NextResponse.redirect(redirectUrl);
    }

    // Convert roles to UserRole enums (userRoles may have been modified above)
    const userRoleEnums: UserRole[] = userRoles.map(role => role as UserRole);

    console.log(
      `Middleware: Authenticated user ${user.email} with roles:`,
      userRoleEnums,
    );

    for (const routePrefix in protectedRoutes) {
      if (currentPath.startsWith(routePrefix)) {
        const requiredRoles = protectedRoutes[routePrefix];

        // ENHANCED DEBUG LOG: What roles did we actually validated?
        console.log(
          `🔍 MIDDLEWARE ROLE CHECK: Checking path ${currentPath}. User roles validated: [${userRoleEnums.join(
            ", ",
          )}]. Required roles: [${requiredRoles.join(", ")}]`,
        );

        // Check if the user has at least one of the required roles
        const hasRequiredRole = userRoleEnums.some((userRole) =>
          requiredRoles.includes(userRole),
        );

        console.log(`🔍 ROLE MATCH RESULT: hasRequiredRole = ${hasRequiredRole}`);

        if (!hasRequiredRole) {
          console.warn(
            `Middleware: Role mismatch for ${currentPath}. User ${userEmail} (roles: ${userRoleEnums.join(",") || "none"}) lacks required roles: ${requiredRoles.join(", ")}. Redirecting.`,
          );

          // PREVENT REDIRECT LOOP: Don't redirect to dashboard if we're already there with an error
          if (currentPath.startsWith("/dashboard") && request.nextUrl.searchParams.has("error")) {
            console.error("Middleware: Redirect loop detected on dashboard. Redirecting to login.");
            return NextResponse.redirect(new URL("/login?error=role_loop", request.url));
          }

          // Redirect unauthorized roles away
          const defaultRedirect = userRoleEnums.includes(UserRole.Client)
            ? "/client-portal"
            : "/login"; // Changed: redirect to login instead of dashboard to prevent loops

          return NextResponse.redirect(
            new URL(defaultRedirect + "?error=role_unauthorized", request.url),
          );
        }
        // Role is valid for this route, break the loop
        console.log(
          `Middleware: Roles '${userRoleEnums.join(",")}' authorized for ${currentPath}.`,
        );
        break;
      }
    }
  }

  // Allow request to proceed for public routes or authorized users
  return response;
}

// Config to specify which paths the middleware should run on
export const config = {
  matcher: [
    /*
     * SIMPLIFIED: Match ALL paths except static files
     * This should definitely catch /dashboard
     */
    "/((?!_next/static|_next/image|favicon.ico).*)",
  ],
};
