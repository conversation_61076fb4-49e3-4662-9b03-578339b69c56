"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useRef } from "react";
import { useRouter } from "next/navigation";
import {
  Search,
  Filter,
  Plus,
  MoreHorizontal,
  Briefcase,
  FileText,
  Calendar,
  AlertTriangle,
  Clock,
  User,
} from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { useMatters<PERSON><PERSON> } from "@/hooks/useMattersApi";
import {
  Matter,
  PracticeArea,
  WorkType,
  getMatterDisplayLabel,
  getPracticeAreaDisplayName,
} from "@/types/domain/tenants/Matter";

// Status filter options
const MATTER_STATUSES = [
  { id: "all", label: "All Statuses" },
  { id: "active", label: "Active" },
  { id: "pending", label: "Pending" },
  { id: "closed", label: "Closed" },
  { id: "rejected", label: "Rejected" },
  { id: "on_hold", label: "On Hold" },
  { id: "settled", label: "Settled" },
  { id: "archived", label: "Archived" },
];

// Practice area filter options (now using specific practice areas)
const PRACTICE_AREAS = [
  { id: "all", label: "All Practice Areas" },
  ...Object.values(PracticeArea).map((area) => ({
    id: area,
    label: getPracticeAreaDisplayName(area),
  })),
];

// Work type filter options
const WORK_TYPES = [
  { id: "all", label: "All Work Types" },
  { id: WorkType.LITIGATION, label: "Litigation" },
  { id: WorkType.TRANSACTIONAL, label: "Transactional" },
  { id: WorkType.ADVISORY, label: "Advisory" },
  { id: WorkType.ADR, label: "Alternative Dispute Resolution" },
];

/**
 * Matters page - displays matters data with dynamic labeling
 * Shows "Cases" for litigation-only firms, "Matters" for mixed practice areas
 */
export default function MattersPage() {
  const router = useRouter();
  const { toast } = useToast();
  const { getAllMatters, getMatterStats } = useMattersApi();

  // States
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [practiceAreaFilter, setPracticeAreaFilter] = useState("all");
  const [workTypeFilter, setWorkTypeFilter] = useState("all");
  const [matters, setMatters] = useState<Matter[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [, setTotalPages] = useState(1);
  const [navigationLabel, setNavigationLabel] = useState("Matters"); // Dynamic label

  // Ref to track if fetchData is in progress
  const fetchInProgress = useRef(false);

  // Stats
  const [stats, setStats] = useState({
    totalMatters: 0,
    activeMatters: 0,
    upcomingDeadlines: 0,
    recentDocuments: 0,
  });

  // Fetch matters and stats using new Matter services
  const fetchData = useCallback(async () => {
    // Prevent multiple simultaneous calls
    if (fetchInProgress.current) {
      console.log("Debug: fetchData already in progress, skipping");
      return;
    }

    console.log("Debug: Starting fetchData");
    fetchInProgress.current = true;
    setLoading(true);
    try {
      // Build filters
      const filters: Record<string, unknown> = {
        page,
        limit: 10,
      };

      if (searchQuery.trim()) {
        filters.search = searchQuery.trim();
      }

      if (statusFilter !== "all") {
        filters.status = statusFilter;
      }

      if (practiceAreaFilter !== "all") {
        filters.practice_area = practiceAreaFilter;
      }

      if (workTypeFilter !== "all") {
        filters.work_type = workTypeFilter;
      }

      // Fetch all matters
      const mattersData = await getAllMatters(filters);
      setMatters(mattersData);

      // Determine navigation label based on user's matters
      const allMatters = await getAllMatters({ limit: 50 });
      const hasMatters = allMatters && allMatters.length > 0;

      // Debug: Add error handling around the suspected problematic line
      let allLitigation = false;
      try {
        console.log("Debug: allMatters:", allMatters);
        console.log("Debug: hasMatters:", hasMatters);
        if (hasMatters) {
          console.log("Debug: First matter workType:", allMatters[0]?.workType);
          console.log("Debug: WorkType.LITIGATION:", WorkType.LITIGATION);
        }
        allLitigation =
          hasMatters &&
          allMatters.every((matter) => {
            console.log(
              "Debug: matter.workType:",
              matter.workType,
              "comparing to:",
              WorkType.LITIGATION,
            );
            return matter.workType === WorkType.LITIGATION;
          });
        console.log("Debug: allLitigation result:", allLitigation);
      } catch (error) {
        console.error("Debug: Error in allLitigation check:", error);
        allLitigation = false; // Default to false on error
      }

      setNavigationLabel(allLitigation ? "Cases" : "Matters");

      // Fetch stats
      console.log("Debug: About to fetch stats");
      const statsData = await getMatterStats();
      console.log("Debug: statsData received:", statsData);

      if (statsData) {
        console.log("Debug: Processing stats data");
        setStats({
          totalMatters:
            ((statsData as Record<string, unknown>)?.total as number) || 0,
          activeMatters:
            ((statsData as Record<string, unknown>)?.active as number) || 0,
          upcomingDeadlines:
            ((statsData as Record<string, unknown>)
              ?.upcomingDeadlines as number) || 0,
          recentDocuments:
            ((statsData as Record<string, unknown>)
              ?.recentDocuments as number) || 0,
        });
        console.log("Debug: Stats set successfully");
      }

      // For now, assume single page (can be enhanced later)
      console.log(
        "Debug: Setting total pages, mattersData.length:",
        mattersData.length,
      );
      setTotalPages(Math.ceil((mattersData.length || 0) / 10));
      console.log("Debug: fetchData completed successfully");
    } catch (error) {
      console.error("Debug: Error caught in fetchData:", error);
      // Check if error is an instance of Error before accessing message
      const errorMessage =
        error instanceof Error ? error.message : "Failed to load matters";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      // Set empty matters on error
      setMatters([]);
      setTotalPages(1);
    } finally {
      console.log("Debug: Finally block reached, setting loading to false");
      fetchInProgress.current = false;
      setLoading(false);
    }
  }, [
    searchQuery,
    statusFilter,
    practiceAreaFilter,
    workTypeFilter,
    page,
    getAllMatters,
    getMatterStats,
  ]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Handle search
  const handleSearch = () => {
    setPage(1); // Reset to first page when searching
    fetchData();
  };

  // Handle filter changes
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    setPage(1); // Reset to first page when filtering
  };

  const handlePracticeAreaFilterChange = (value: string) => {
    setPracticeAreaFilter(value);
    setPage(1); // Reset to first page when filtering
  };

  const handleWorkTypeFilterChange = (value: string) => {
    setWorkTypeFilter(value);
    setPage(1); // Reset to first page when filtering
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchQuery("");
    setStatusFilter("all");
    setPracticeAreaFilter("all");
    setWorkTypeFilter("all");
    setPage(1);
  };

  // Helper functions
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "active":
        return "default";
      case "pending":
        return "secondary";
      case "closed":
        return "outline";
      case "rejected":
        return "destructive";
      case "on_hold":
        return "secondary";
      case "settled":
        return "default";
      case "archived":
        return "outline";
      default:
        return "secondary";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "text-red-600";
      case "high":
        return "text-orange-600";
      case "medium":
        return "text-yellow-600";
      case "low":
        return "text-green-600";
      default:
        return "text-gray-600";
    }
  };

  const renderPriority = (priority: string) => {
    const color = getPriorityColor(priority);
    return (
      <div className={`flex items-center ${color}`}>
        <AlertTriangle className="h-3 w-3 mr-1" />
        <span className="capitalize">{priority}</span>
      </div>
    );
  };

  const getDaysFromNow = (dateString: string | null) => {
    if (!dateString) return null;
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Not set";
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="container max-w-7xl mx-auto py-8 px-4">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">
              Loading {navigationLabel.toLowerCase()}...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-7xl mx-auto py-8 px-4">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {navigationLabel}
          </h1>
          <p className="text-muted-foreground mt-1">
            Manage all your active and pending {navigationLabel.toLowerCase()}
          </p>
        </div>
        <div className="mt-4 lg:mt-0">
          <Button onClick={() => router.push("/matters/new")}>
            <Plus className="mr-2 h-4 w-4" />
            New {navigationLabel.slice(0, -1)}
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Total {navigationLabel}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalMatters}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Across all practice areas
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Active {navigationLabel}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeMatters}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Currently in progress
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Upcoming Deadlines
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.upcomingDeadlines}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Across all {navigationLabel.toLowerCase()}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              {navigationLabel} Documents
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.recentDocuments}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Total linked documents
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search and filters */}
      <Card className="mb-6">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center">
            <Filter className="h-4 w-4 mr-2" />
            Search and Filter
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={`Search ${navigationLabel.toLowerCase()} or clients...`}
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && handleSearch()}
                />
              </div>
            </div>

            <div>
              <Select
                value={statusFilter}
                onValueChange={handleStatusFilterChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  {MATTER_STATUSES.map((status) => (
                    <SelectItem key={status.id} value={status.id}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Select
                value={practiceAreaFilter}
                onValueChange={handlePracticeAreaFilterChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Practice Area" />
                </SelectTrigger>
                <SelectContent>
                  {PRACTICE_AREAS.map((area) => (
                    <SelectItem key={area.id} value={area.id}>
                      {area.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Select
                value={workTypeFilter}
                onValueChange={handleWorkTypeFilterChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Work Type" />
                </SelectTrigger>
                <SelectContent>
                  {WORK_TYPES.map((workType) => (
                    <SelectItem key={workType.id} value={workType.id}>
                      {workType.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex justify-between items-center mt-4">
            <Button variant="outline" onClick={handleSearch}>
              <Search className="mr-2 h-4 w-4" />
              Apply Filters
            </Button>

            {(searchQuery ||
              statusFilter !== "all" ||
              practiceAreaFilter !== "all" ||
              workTypeFilter !== "all") && (
              <Button variant="ghost" onClick={clearFilters}>
                Clear filters
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Matters Table */}
      <Card>
        <CardContent className="pt-6">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mr-3"></div>
              <span>Loading {navigationLabel.toLowerCase()}...</span>
            </div>
          ) : matters.length === 0 ? (
            <div className="text-center py-12">
              <div className="flex flex-col items-center space-y-4">
                <div className="rounded-full bg-muted p-3">
                  <Briefcase className="h-6 w-6 text-muted-foreground" />
                </div>
                <div>
                  <h3 className="text-lg font-medium">
                    No {navigationLabel.toLowerCase()} found
                  </h3>
                  <p className="text-muted-foreground mt-1">
                    {searchQuery ||
                    statusFilter !== "all" ||
                    practiceAreaFilter !== "all" ||
                    workTypeFilter !== "all"
                      ? `No ${navigationLabel.toLowerCase()} match your current filters.`
                      : `Get started by creating your first ${navigationLabel.toLowerCase().slice(0, -1)}.`}
                  </p>
                </div>
                <div className="flex space-x-2">
                  {(searchQuery ||
                    statusFilter !== "all" ||
                    practiceAreaFilter !== "all" ||
                    workTypeFilter !== "all") && (
                    <Button variant="outline" onClick={clearFilters}>
                      Clear filters
                    </Button>
                  )}
                  <Button onClick={() => router.push("/matters/new")}>
                    <Plus className="mr-2 h-4 w-4" />
                    Create {navigationLabel.slice(0, -1)}
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{navigationLabel.slice(0, -1)}</TableHead>
                  <TableHead>Client</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Practice Area</TableHead>
                  <TableHead>Work Type</TableHead>
                  <TableHead>Filed Date</TableHead>
                  <TableHead>Next Deadline</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {matters.map((matter) => {
                  const daysUntilDeadline = getDaysFromNow(
                    matter.trialDate ?? null,
                  );
                  const matterLabel = getMatterDisplayLabel(matter.workType);

                  return (
                    <TableRow
                      key={matter.id}
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => router.push(`/matters/${matter.id}`)}
                    >
                      <TableCell>
                        <div>
                          <div className="font-medium">{matter.title}</div>
                          {matter.caseNumber && (
                            <div className="text-sm text-muted-foreground">
                              {matter.caseNumber}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {matter.clients && matter.clients.length > 0 ? (
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-2 text-muted-foreground" />
                            <div>
                              <div className="font-medium">
                                {matter.clients[0].fullName}
                              </div>
                              {matter.clients.length > 1 && (
                                <div className="text-sm text-muted-foreground">
                                  +{matter.clients.length - 1} more
                                </div>
                              )}
                            </div>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">None</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadgeVariant(matter.status)}>
                          {matter.status.replace("_", " ")}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {getPracticeAreaDisplayName(matter.practiceArea)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">
                          {matter.workType === WorkType.LITIGATION
                            ? "Litigation"
                            : matter.workType === WorkType.TRANSACTIONAL
                              ? "Transactional"
                              : matter.workType === WorkType.ADVISORY
                                ? "Advisory"
                                : "ADR"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                          {formatDate(matter.filingDate)}
                        </div>
                      </TableCell>
                      <TableCell>
                        {matter.trialDate ? (
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                            <div>
                              <div>{formatDate(matter.trialDate)}</div>
                              {daysUntilDeadline !== null && (
                                <div
                                  className={`text-sm ${
                                    daysUntilDeadline < 0
                                      ? "text-red-600"
                                      : daysUntilDeadline <= 7
                                        ? "text-orange-600"
                                        : "text-muted-foreground"
                                  }`}
                                >
                                  {daysUntilDeadline < 0
                                    ? `${Math.abs(daysUntilDeadline)} days overdue`
                                    : daysUntilDeadline === 0
                                      ? "Due today"
                                      : `${daysUntilDeadline} days left`}
                                </div>
                              )}
                            </div>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">None</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {renderPriority(matter.priorityLevel || "medium")}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent
                            onClick={(e) => e.stopPropagation()}
                            align="end"
                          >
                            <DropdownMenuItem
                              onClick={() =>
                                router.push(`/matters/${matter.id}`)
                              }
                            >
                              <Briefcase className="mr-2 h-4 w-4" />
                              View {matterLabel}
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() =>
                                router.push(`/matters/${matter.id}/edit`)
                              }
                            >
                              <FileText className="mr-2 h-4 w-4" />
                              Edit Details
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() =>
                                router.push(`/matters/${matter.id}/documents`)
                              }
                            >
                              <FileText className="mr-2 h-4 w-4" />
                              View Documents
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() =>
                                router.push(`/matters/${matter.id}/deadlines`)
                              }
                            >
                              <Calendar className="mr-2 h-4 w-4" />
                              Manage Deadlines
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
