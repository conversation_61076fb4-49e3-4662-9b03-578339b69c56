'use client'

import { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { ArrowLeft, Save, User, Building, FileText, Gavel, Briefcase, Calculator, Calendar } from 'lucide-react'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { useToast } from '@/components/ui/use-toast'
import { useMattersApi } from '@/hooks/useMattersApi'
import { PracticeArea, MatterStatus, MatterPriorityLevel } from '@/types/domain/tenants/Matter'
import { PRACTICE_AREA_GROUPS, getPracticeAreaOption, getWorkTypeFromPracticeArea } from '@/config/practice-areas'

// Form data interface
interface NewMatterFormData {
  title: string
  description: string
  practiceArea: PracticeArea | ''
  specificPracticeArea: string // e.g., 'personal_injury', 'criminal_defense'
  clientId: string
  status: MatterStatus
  sensitive: boolean
  priorityLevel: MatterPriorityLevel
  trialDate: string
  metadata: Record<string, unknown>
}

export default function NewMatterPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { createMatter } = useMattersApi()

  // Form state
  const [formData, setFormData] = useState<NewMatterFormData>({
    title: '',
    description: '',
    practiceArea: '',
    specificPracticeArea: '',
    clientId: '',
    status: MatterStatus.PENDING,
    sensitive: false,
    priorityLevel: MatterPriorityLevel.MEDIUM,
    trialDate: '',
    metadata: {}
  })

  const [loading, setLoading] = useState(false)
  const [clients, setClients] = useState<Array<{ id: string; fullName: string }>>([])

  // Load clients for selection
  useEffect(() => {
    // TODO: Implement client fetching
    // For now, using mock data
    setClients([
      { id: '1', fullName: 'John Doe' },
      { id: '2', fullName: 'Jane Smith' }
    ])
  }, [])

  // Handle form field changes
  const handleInputChange = (field: keyof NewMatterFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // Handle specific practice area change (automatically sets work type)
  const handleSpecificPracticeAreaChange = (specificPracticeArea: string) => {
    const workType = getWorkTypeFromPracticeArea(specificPracticeArea)
    setFormData(prev => ({
      ...prev,
      specificPracticeArea,
      practiceArea: workType || ''
    }))
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validation
    if (!formData.title.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Matter title is required',
        variant: 'destructive'
      })
      return
    }

    if (!formData.specificPracticeArea) {
      toast({
        title: 'Validation Error',
        description: 'Practice area is required',
        variant: 'destructive'
      })
      return
    }

    if (!formData.clientId) {
      toast({
        title: 'Validation Error',
        description: 'Client selection is required',
        variant: 'destructive'
      })
      return
    }

    setLoading(true)
    
    try {
      // Prepare matter data
      const matterData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        practiceArea: formData.practiceArea as PracticeArea,
        clientId: formData.clientId,
        status: formData.status,
        sensitive: formData.sensitive,
        priorityLevel: formData.priorityLevel,
        trialDate: formData.trialDate || null,
        metadata: {
          ...formData.metadata,
          specificPracticeArea: formData.specificPracticeArea
        }
      }

      const newMatter = await createMatter(matterData)
      
      if (!newMatter || typeof newMatter !== 'object' || !('id' in newMatter)) {
        throw new Error('Invalid response from server')
      }
      
      toast({
        title: 'Success',
        description: 'Matter created successfully',
      })

      // Navigate to the new matter
      router.push(`/matters/${(newMatter as { id: string }).id}`)
      
    } catch (error) {
      console.error('Error creating matter:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create matter',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  // Handle cancel
  const handleCancel = () => {
    router.push('/matters')
  }

  // Get matter type label based on practice area
  const getMatterTypeLabel = () => {
    if (!formData.specificPracticeArea) return 'Matter'
    const workType = getWorkTypeFromPracticeArea(formData.specificPracticeArea)
    return workType === PracticeArea.LITIGATION ? 'Case' : 'Matter'
  }

  return (
    <div className="container max-w-4xl mx-auto py-8 px-4">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Button variant="outline" size="icon" onClick={handleCancel}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Create New {getMatterTypeLabel()}</h1>
          <p className="text-muted-foreground mt-1">
            {formData.specificPracticeArea
              ? `Add a new ${getPracticeAreaOption(formData.specificPracticeArea)?.label.toLowerCase()} ${getMatterTypeLabel().toLowerCase()} to your practice`
              : `Add a new ${getMatterTypeLabel().toLowerCase()} to your practice`
            }
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Matter Title *</Label>
              <Input
                id="title"
                placeholder="Enter matter title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Brief description of the matter"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="practiceArea">Practice Area *</Label>
                <Select
                  value={formData.specificPracticeArea}
                  onValueChange={handleSpecificPracticeAreaChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select practice area" />
                  </SelectTrigger>
                  <SelectContent className="max-h-[400px]">
                    {PRACTICE_AREA_GROUPS.map((group) => (
                      <div key={group.workType}>
                        <div className="px-2 py-1.5 text-sm font-semibold text-muted-foreground bg-muted/50">
                          {group.label} - {group.description}
                        </div>
                        {group.options.map((option) => (
                          <SelectItem key={option.practiceAreaCode} value={option.practiceAreaCode}>
                            <div className="flex flex-col gap-1 py-1">
                              <div className="font-medium">{option.label}</div>
                              <div className="text-xs text-muted-foreground">{option.description}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </div>
                    ))}
                  </SelectContent>
                </Select>
                {formData.specificPracticeArea && (
                  <div className="mt-2 p-2 bg-muted/30 rounded-md">
                    <div className="text-xs font-medium text-muted-foreground">Selected:</div>
                    <div className="text-sm font-medium">{getPracticeAreaOption(formData.specificPracticeArea)?.label}</div>
                    <div className="text-xs text-muted-foreground">
                      Work Type: {getPracticeAreaOption(formData.specificPracticeArea)?.workType === PracticeArea.LITIGATION ? 'Case' : 'Matter'}
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Initial Status</Label>
                <Select 
                  value={formData.status} 
                  onValueChange={(value) => handleInputChange('status', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={MatterStatus.PENDING}>Pending</SelectItem>
                    <SelectItem value={MatterStatus.ACTIVE}>Active</SelectItem>
                    <SelectItem value={MatterStatus.ON_HOLD}>On Hold</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Client Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Client Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="client">Client *</Label>
              <Select 
                value={formData.clientId} 
                onValueChange={(value) => handleInputChange('clientId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a client" />
                </SelectTrigger>
                <SelectContent>
                  {clients.map((client) => (
                    <SelectItem key={client.id} value={client.id}>
                      {client.fullName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Matter Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Briefcase className="h-5 w-5" />
              {getMatterTypeLabel()} Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="priority">Priority Level</Label>
                <Select 
                  value={formData.priorityLevel} 
                  onValueChange={(value) => handleInputChange('priorityLevel', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={MatterPriorityLevel.LOW}>Low</SelectItem>
                    <SelectItem value={MatterPriorityLevel.MEDIUM}>Medium</SelectItem>
                    <SelectItem value={MatterPriorityLevel.HIGH}>High</SelectItem>
                    <SelectItem value={MatterPriorityLevel.URGENT}>Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="trialDate">
                  {formData.practiceArea === PracticeArea.LITIGATION ? 'Trial Date' : 'Key Deadline'}
                </Label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="trialDate"
                    type="date"
                    value={formData.trialDate}
                    onChange={(e) => handleInputChange('trialDate', e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="sensitive"
                checked={formData.sensitive}
                onCheckedChange={(checked) => handleInputChange('sensitive', checked)}
              />
              <Label htmlFor="sensitive">Mark as sensitive/confidential</Label>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Create {getMatterTypeLabel()}
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}