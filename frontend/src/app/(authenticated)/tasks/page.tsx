//frontend/src/app/(authenticated)/tasks/page.tsx
'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { format } from 'date-fns'
import { Plus, Pencil, Trash2, ChevronDown, ChevronUp } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { DatePicker } from '@/components/ui/date-picker'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { zodResolver } from '@hookform/resolvers/zod'
import { CalendarIcon, ListFilter } from 'lucide-react'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

import { useTasksApi, type Task, type TaskWithRelations } from '@/hooks/useTasksApi'
import { useSupabase } from '@/lib/supabase/provider'
import { useSession } from '@/contexts/SessionContext'
import { toast } from 'sonner'
import { useUser } from '@/contexts/UserContext'
// import { debugJwtAndRoles } from '@/lib/debug/jwtrole'
import { User } from '@supabase/supabase-js'

// Task form schema
const taskFormSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  due_date: z.string().optional(),
  status: z.enum(['todo', 'in_progress', 'done']).default('todo'),
  assigned_to: z.string().uuid().optional(),
  related_case: z.string().uuid().optional(),
})

export default function TasksPage() {
  const router = useRouter();
  const { supabase } = useSupabase()
  const { session } = useSession()
  const { user: currentUser, refreshUser } = useUser();

  // State variables
  const [isLoading, setIsLoading] = useState(false);
  const [tasks, setTasks] = useState<Task[]>([])
  const [users, setUsers] = useState<any[]>([])
  const [cases, setCases] = useState<any[]>([])
  const [editingTask, setEditingTask] = useState<Task | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [expandedTask, setExpandedTask] = useState<string | null>(null)
  const [selectedTasks, setSelectedTasks] = useState<string[]>([])
  const [bulkActionOpen, setBulkActionOpen] = useState(false)
  const [bulkStatus, setBulkStatus] = useState('done')

  // Use schema-aware task operations
  const {
    getAllTasks,
    getTaskWithRelations,
    getTaskHistory,
    createTask,
    updateTask,
    deleteTask,
    isLoading: tasksLoading,
    error: tasksError
  } = useTasksApi();

  // Form setup
  const form = useForm<z.infer<typeof taskFormSchema>>({
    resolver: zodResolver(taskFormSchema),
    defaultValues: {
      title: '',
      description: '',
      status: 'todo',
      due_date: undefined,
      assigned_to: undefined,
      related_case: undefined,
    },
  })

  // Main data fetching function
  const fetchData = async () => {
    setIsLoading(true)

    try {
      // Check for active session
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        console.error('No active session found when fetching tasks');
        toast.error('Please sign in again to access your tasks');
        setTasks([]);
        return;
      }

      // Debug JWT claims in development
      if (process.env.NODE_ENV !== 'production') {
        try {
          // JWT debugging removed - use debugJwtAndRoles if needed
          console.log("Session available when fetching tasks:", !!session);
        } catch (err) {
          console.log("Could not debug session", err);
        }
      }

      // Get current user
      await refreshUser();

      // Fetch tasks using schema-aware hook
      const tasksData = await getAllTasks()
      console.log('Tasks fetched successfully:', tasksData.length);
      setTasks(tasksData || [])
    } catch (error) {
      console.error('Error fetching tasks:', error)
      toast.error('Failed to load tasks. Please refresh the page.')
      setTasks([])
    }

    try {
      // Fetch users with schema awareness
      const { data: usersData, error: usersError } = await supabase
        .schema('tenants')
        .from('users')
        .select('*')

      if (usersError) {
        console.error('Error fetching users:', usersError)
        setUsers([])
      } else {
        setUsers(usersData || [])
      }
    } catch (error) {
      console.error('Error fetching users:', error)
      setUsers([])
    }

    try {
      // Fetch matters (cases) with schema awareness
      const { data: casesData, error: casesError } = await supabase
        .schema('tenants')
        .from('matters')
        .select('*')

      if (casesError) {
        console.error('Error fetching cases:', casesError)
        setCases([])
      } else {
        setCases(casesData || [])
      }
    } catch (error) {
      console.error('Error fetching cases:', error)
      setCases([])
    }

    setIsLoading(false)
  }

  // Fetch data on component mount
  useEffect(() => {
    fetchData()

    // Set up refresh interval instead of realtime subscriptions
    const refreshInterval = setInterval(() => {
      fetchData();
    }, 30000); // Refresh every 30 seconds

    return () => {
      clearInterval(refreshInterval);
    };
  }, []) // eslint-disable-line react-hooks/exhaustive-deps

  // Toggle task status
  const handleToggleStatus = async (task: Task) => {
    try {
      const newStatus = task.status === 'done' ? 'todo' : 'done'
      await updateTask(task.id, {
        status: newStatus,
      })
      toast.success(`Task marked as ${newStatus}`)
      fetchData() // Refresh tasks
    } catch (error) {
      console.error('Error toggling task status:', error)
      toast.error('Failed to update task status')
    }
  }

  // Reset form when dialog closes
  useEffect(() => {
    if (!isDialogOpen) {
      form.reset({
        title: '',
        description: '',
        status: 'todo',
        due_date: undefined,
        assigned_to: undefined,
        related_case: undefined,
      })
      setEditingTask(null)
    }
  }, [isDialogOpen, form])

  // Update form value when due date changes
  const [dueDate, setDueDate] = useState<Date | undefined>(undefined)
  useEffect(() => {
    if (dueDate) {
      form.setValue('due_date', dueDate.toISOString().split('T')[0])
    } else {
      form.setValue('due_date', '')
    }
  }, [dueDate, form])

  // Form submission handler
  const onSubmit = async (data: z.infer<typeof taskFormSchema>) => {
    try {
      if (editingTask) {
        // Update existing task using schema-aware hook
        await updateTask(editingTask.id, {
          ...data,
        })
        toast.success('Task updated successfully')
      } else {
        // Create new task using schema-aware hook
        await createTask({
          ...data,
        })
        toast.success('Task created successfully')
      }

      setIsDialogOpen(false)
      setEditingTask(null)
      form.reset()
      fetchData() // Refresh the task list
    } catch (error) {
      console.error('Error submitting task:', error)
      toast.error('Failed to save task. Please try again.')
    }
  }

  // Handle task editing - fetch detailed task with relations
  const handleTaskClick = async (task: Task) => {
    try {
      // Fetch detailed task with relations when editing
      const detailedTask = await getTaskWithRelations(task.id);

      // If we got detailed data, use it; otherwise fall back to the basic task
      const taskToEdit = detailedTask || task;

      // Reset form with task data - with proper type casting
      form.reset({
        title: taskToEdit.title || '',
        description: taskToEdit.description || '',
        status: (taskToEdit.status as 'todo' | 'in_progress' | 'done') || 'todo',
        due_date: taskToEdit.due_date ? new Date(taskToEdit.due_date).toISOString().split('T')[0] : undefined,
        assigned_to: taskToEdit.assigned_to || undefined,
        related_case: typeof taskToEdit.related_case === 'object' && taskToEdit.related_case?.id
          ? taskToEdit.related_case.id
          : (taskToEdit.related_case as string | undefined),
      })

      // Store the task for editing
      setEditingTask(taskToEdit)
      setIsDialogOpen(true)
    } catch (error) {
      console.error('Error fetching task details:', error)
      toast.error('Failed to load task details')

      // Fall back to basic task data if detailed fetch fails - with proper type casting
      form.reset({
        title: task.title || '',
        description: task.description || '',
        status: (task.status as 'todo' | 'in_progress' | 'done') || 'todo',
        due_date: task.due_date ? new Date(task.due_date).toISOString().split('T')[0] : undefined,
        assigned_to: task.assigned_to || undefined,
        related_case: typeof task.related_case === 'object' && task.related_case?.id
          ? task.related_case.id
          : (task.related_case as string | undefined),
      })
      setEditingTask(task)
      setIsDialogOpen(true)
    }
  }

  // Delete a task with schema-aware hook
  const handleDeleteTask = async (id: string) => {
    try {
      await deleteTask(id)
      toast.success('Task deleted successfully')
      setSelectedTasks((current) => current.filter((taskId) => taskId !== id))
      fetchData() // Refresh the task list
    } catch (error) {
      console.error('Error deleting task:', error)
      toast.error('Failed to delete task')
    }
  }

  // Selection handlers for bulk operations
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedTasks(tasks.map(task => task.id))
    } else {
      setSelectedTasks([])
    }
  }

  // Toggle selection of a single task
  const handleSelectTask = (taskId: string, checked: boolean) => {
    if (checked) {
      setSelectedTasks(prev => [...prev, taskId])
    } else {
      setSelectedTasks(prev => prev.filter(id => id !== taskId))
    }
  }

  // Expand/collapse task details
  const handleExpandTask = async (taskId: string) => {
    if (expandedTask === taskId) {
      setExpandedTask(null) // Collapse if already expanded
      return;
    }

    try {
      // Fetch detailed task data including relations when expanding
      await getTaskWithRelations(taskId);
      setExpandedTask(taskId) // Expand the clicked task
    } catch (error) {
      console.error('Error fetching task details for expansion:', error)
      toast.error('Failed to load task details');
      // Still expand, but without detailed data
      setExpandedTask(taskId)
    }
  }

  // Update status for multiple tasks at once
  const handleBulkStatusUpdate = async () => {
    try {
      // Update all selected tasks using schema-aware hook
      await Promise.all(
        selectedTasks.map(taskId =>
          updateTask(taskId, {
            status: bulkStatus,
          })
        )
      )
      toast.success(`${selectedTasks.length} tasks updated`)
      setSelectedTasks([])
      setBulkActionOpen(false)
      fetchData() // Refresh data
    } catch (error) {
      console.error('Error updating tasks in bulk:', error)
      toast.error('Failed to update tasks in bulk')
    }
  }

  // Delete multiple tasks at once
  const handleBulkDelete = async () => {
    if (selectedTasks.length === 0) return

    if (
      !confirm(
        `Are you sure you want to delete ${selectedTasks.length} tasks? This cannot be undone.`
      )
    ) {
      return
    }

    try {
      // Use schema-aware deleteTask method
      await Promise.all(selectedTasks.map(taskId => deleteTask(taskId)))
      toast.success(`${selectedTasks.length} tasks deleted`)
      setSelectedTasks([])
      setBulkActionOpen(false)
      fetchData() // Refresh data
    } catch (error) {
      console.error('Error deleting tasks in bulk:', error)
      toast.error('Failed to delete tasks in bulk')
    }
  }

  // Handle task status change from dropdown
  const handleTaskStatusChange = async (taskId: string, newStatus: string) => {
    try {
      await updateTask(taskId, {
        status: newStatus
      })
      toast.success(`Task status updated to ${newStatus}`)
      fetchData() // Refresh data
    } catch (error) {
      console.error('Error updating task status:', error)
      toast.error('Failed to update task status')
    }
  }

  // Alias for handleTaskClick for clarity in UI
  const handleEdit = (task: Task) => {
    handleTaskClick(task)
  }

  // Alias for handleDeleteTask for clarity in UI
  const handleDelete = (taskId: string) => {
    handleDeleteTask(taskId)
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-10">
        <Card>
          <CardHeader>
            <CardTitle>Tasks</CardTitle>
            <CardDescription>Loading tasks...</CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-10">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Tasks</CardTitle>
              <CardDescription>Manage your tasks and deadlines</CardDescription>
            </div>
            <div className="flex gap-2">
              {selectedTasks.length > 0 && (
                <Dialog open={bulkActionOpen} onOpenChange={setBulkActionOpen}>
                  <DialogTrigger asChild>
                    <Button variant="outline">
                      Bulk Actions ({selectedTasks.length})
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Bulk Actions</DialogTitle>
                      <DialogDescription>
                        Apply actions to {selectedTasks.length} selected tasks
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                      <div className="space-y-2">
                        <h4 className="font-medium">Update Status</h4>
                        <select
                          value={bulkStatus}
                          onChange={(e) => setBulkStatus(e.target.value as 'todo' | 'in_progress' | 'done')}
                          className="w-full p-2 border rounded-md"
                        >
                          <option value="todo">To Do</option>
                          <option value="in_progress">In Progress</option>
                          <option value="done">Done</option>
                        </select>
                        <Button
                          onClick={handleBulkStatusUpdate}
                          className="w-full mt-2"
                        >
                          Update Status
                        </Button>
                      </div>
                      <div className="space-y-2">
                        <h4 className="font-medium">Delete Tasks</h4>
                        <Button
                          onClick={handleBulkDelete}
                          variant="destructive"
                          className="w-full"
                        >
                          Delete {selectedTasks.length} Tasks
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              )}
              <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Task
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>{editingTask ? 'Edit Task' : 'New Task'}</DialogTitle>
                    <DialogDescription>
                      {editingTask ? 'Edit the task details below.' : 'Add a new task to your list.'}
                    </DialogDescription>
                  </DialogHeader>
                  <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                      <FormField
                        control={form.control}
                        name="title"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Title</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="Task title" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Description</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="Task description" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="due_date"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel>Due Date</FormLabel>
                            <DatePicker
                              date={dueDate}
                              setDate={setDueDate}
                              placeholder="Select due date"
                            />
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="status"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Status</FormLabel>
                            <FormControl>
                              <select
                                {...field}
                                className="w-full p-2 border rounded-md"
                              >
                                <option value="todo">To Do</option>
                                <option value="in_progress">In Progress</option>
                                <option value="done">Done</option>
                              </select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="assigned_to"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Assign To</FormLabel>
                            <FormControl>
                              <select
                                {...field}
                                className="w-full p-2 border rounded-md"
                                value={field.value || ''}
                              >
                                <option value="">Unassigned</option>
                                {users.map((user) => (
                                  <option key={user.id} value={user.id}>
                                    {user.email} ({user.role})
                                  </option>
                                ))}
                              </select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="related_case"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Related Case</FormLabel>
                            <FormControl>
                              <select
                                {...field}
                                className="w-full p-2 border rounded-md"
                                value={field.value || ''}
                              >
                                <option value="">No Case</option>
                                {cases.map((case_) => (
                                  <option key={case_.id} value={case_.id}>
                                    {case_.title} ({case_.status})
                                  </option>
                                ))}
                              </select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <DialogFooter>
                        <Button type="submit">
                          {editingTask ? 'Update Task' : 'Create Task'}
                        </Button>
                      </DialogFooter>
                    </form>
                  </Form>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox
                    onCheckedChange={handleSelectAll}
                    checked={selectedTasks.length > 0 && selectedTasks.length === tasks.length}
                  />
                </TableHead>
                <TableHead className="w-32">Status</TableHead>
                <TableHead>Title</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Assigned To</TableHead>
                <TableHead>Related Case</TableHead>
                <TableHead>Due Date</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {tasks.map((task) => (
                <React.Fragment key={task.id}>
                  <TableRow
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => setExpandedTask(expandedTask === task.id ? null : task.id)}
                  >
                    <TableCell onClick={(e) => e.stopPropagation()}>
                      <Checkbox
                        checked={selectedTasks.includes(task.id)}
                        onCheckedChange={(checked) => handleSelectTask(task.id, !!checked)}
                      />
                    </TableCell>
                    <TableCell onClick={(e) => e.stopPropagation()}>
                      <select
                        value={task.status}
                        onChange={(e) => handleTaskStatusChange(task.id, e.target.value)}
                        className="p-1 border rounded-md"
                      >
                        <option value="todo">To Do</option>
                        <option value="in_progress">In Progress</option>
                        <option value="done">Done</option>
                      </select>
                    </TableCell>
                    <TableCell className="font-medium">
                      <Link href={`/tasks/${task.id}`} className="hover:underline">
                        {task.title}
                      </Link>
                    </TableCell>
                    <TableCell>{task.description || '-'}</TableCell>
                    <TableCell>
                      {users.find(u => u.id === task.assigned_to)?.email || 'Unassigned'}
                    </TableCell>
                    <TableCell>
                      {cases.find(c => c.id === (typeof task.related_case === 'object' ? task.related_case?.id : task.related_case))?.title || 'N/A'}
                    </TableCell>
                    <TableCell>
                      {task.due_date ? format(new Date(task.due_date), 'MMM dd, yyyy') : '-'}
                    </TableCell>
                    <TableCell>{task.created_at ? format(new Date(task.created_at), 'MMM dd, yyyy') : '-'}</TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleEdit(task)
                        }}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDelete(task.id)
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => {
                          e.stopPropagation()
                          setExpandedTask(expandedTask === task.id ? null : task.id)
                        }}
                      >
                        {expandedTask === task.id ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    </TableCell>
                  </TableRow>
                  {expandedTask === task.id && (
                    <TableRow>
                      <TableCell colSpan={8} className="p-0">
                        <div className="p-4 bg-muted/30">
                          {/* Import the TaskHistory component */}
                          {/* <TaskHistory taskId={task.id} /> */}
                          <div>Task history is temporarily unavailable</div>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </React.Fragment>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
