// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { with<PERSON><PERSON>, AuthUser, UserRole } from '@/lib/auth/server-exports';
import { createServices } from '@/lib/services';
import { z } from 'zod';
import type { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/database.types';

// Define interfaces for matter data
interface Matter {
  id: string;
  title: string;
  description?: string | null;
  sensitive?: boolean;
  client_id: string;
  metadata?: Record<string, unknown>;
  status?: 'pending' | 'active' | 'closed' | 'rejected' | 'on_hold' | 'settled' | 'archived';
  practice_area?: 'litigation' | 'transaction' | 'advisory' | 'ip';
  rejection_reason?: string | null;
  created_at?: string;
  updated_at?: string;
  trial_date?: string | null;
  priority_level?: string;
  [key: string]: any;
}

// Update schema for matter updates
const updateMatterSchema = z.object({
  title: z.string().min(1).optional(),
  description: z.string().nullable().optional(),
  sensitive: z.boolean().optional(),
  client_id: z.string().uuid().optional(),
  practice_area: z.enum(['litigation', 'transaction', 'advisory', 'ip']).optional(),
  metadata: z.record(z.unknown()).optional(),
  status: z.enum(['pending', 'active', 'closed', 'rejected', 'on_hold', 'settled', 'archived']).optional(),
  rejection_reason: z.string().nullable().optional(),
  trial_date: z.string().nullable().optional(),
  priority_level: z.string().optional(),
});

type UpdateMatterData = z.infer<typeof updateMatterSchema>;

// GET /api/matters/[id] - Fetch a specific matter by ID
export const GET = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff],
  async (
    req: NextRequest,
    user: AuthUser,
    supabase: SupabaseClient<Database, "public", Database['public']>,
    context: Record<string, unknown>
  ): Promise<Response> => {
    try {
      const params = context.params as { id: string } | undefined;

      if (!params || !params.id) {
        return NextResponse.json({ error: 'No parameters provided' }, { status: 400 });
      }

      const { id } = params;

      if (!id || !z.string().uuid().safeParse(id).success) {
        return NextResponse.json({ error: 'Invalid matter ID' }, { status: 400 });
      }

      // Validate tenantId is defined
      if (!user.tenantId) {
        console.error('tenantId is undefined in JWT claims');
        return NextResponse.json(
          { error: 'Authorization error: Missing tenant information' },
          { status: 400 }
        );
      }

      // Query the matter directly from Supabase
      const { data: matter, error } = await supabase
        .schema('tenants')
        .from('matters')
        .select(`
          id,
          title,
          description,
          status,
          practice_area,
          display_label,
          sensitive,
          created_at,
          updated_at,
          metadata,
          rejection_reason,
          client_id,
          created_by,
          trial_date,
          priority_level,
          clients:client_id(id, first_name, last_name, email, phone_primary)
        `)
        .eq('id', id)
        .eq('tenant_id', user.tenantId)
        .single();

      if (error) {
        console.error('Error fetching matter:', error);
        if (error.code === 'PGRST116') {
          return NextResponse.json({ error: 'Matter not found' }, { status: 404 });
        }
        return NextResponse.json({ error: 'Failed to fetch matter' }, { status: 500 });
      }

      // Transform the data to match expected format
      const transformedMatter = {
        ...matter,
        clients: matter.clients ? [matter.clients] : [],
        trialDate: matter.trial_date,
        priorityLevel: matter.priority_level,
        practiceArea: matter.practice_area,
        createdAt: matter.created_at,
        updatedAt: matter.updated_at,
        metadata: matter.metadata
      };

      return NextResponse.json(transformedMatter);

    } catch (error) {
      console.error('Error in GET /api/matters/[id]:', error);
      return NextResponse.json(
        { error: 'An unexpected error occurred' },
        { status: 500 }
      );
    }
  }
);

// PUT /api/matters/[id] - Update a matter
export const PUT = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff],
  async (
    req: NextRequest,
    user: AuthUser,
    supabase: SupabaseClient<Database, "public", Database['public']>,
    context: Record<string, unknown>
  ): Promise<Response> => {
    try {
      const params = context.params as { id: string } | undefined;

      if (!params || !params.id) {
        return NextResponse.json({ error: 'No parameters provided' }, { status: 400 });
      }

      const { id } = params;

      if (!id || !z.string().uuid().safeParse(id).success) {
        return NextResponse.json({ error: 'Invalid matter ID' }, { status: 400 });
      }

      // Validate tenantId is defined
      if (!user.tenantId) {
        console.error('tenantId is undefined in JWT claims');
        return NextResponse.json(
          { error: 'Authorization error: Missing tenant information' },
          { status: 400 }
        );
      }

      // Parse and validate request body
      const body = await req.json();
      const validatedData = updateMatterSchema.parse(body);

      // Update the matter
      const updateData = {
        ...validatedData,
        updated_at: new Date().toISOString(),
      };

      const { data: matter, error } = await supabase
        .schema('tenants')
        .from('matters')
        .update(updateData)
        .eq('id', id)
        .eq('tenant_id', user.tenantId)
        .select(`
          id,
          title,
          description,
          status,
          practice_area,
          display_label,
          sensitive,
          created_at,
          updated_at,
          metadata,
          rejection_reason,
          client_id,
          created_by,
          trial_date,
          priority_level,
          clients:client_id(id, full_name, email, phone)
        `)
        .single();

      if (error) {
        console.error('Error updating matter:', error);
        if (error.code === 'PGRST116') {
          return NextResponse.json({ error: 'Matter not found' }, { status: 404 });
        }
        return NextResponse.json({ error: 'Failed to update matter' }, { status: 500 });
      }

      // Transform the data to match expected format
      const transformedMatter = {
        ...matter,
        clients: matter.clients ? [matter.clients] : [],
        trialDate: matter.trial_date,
        priorityLevel: matter.priority_level,
        practiceArea: matter.practice_area,
        createdAt: matter.created_at,
        updatedAt: matter.updated_at,
        metadata: matter.metadata
      };

      return NextResponse.json(transformedMatter);

    } catch (error) {
      console.error('Error in PUT /api/matters/[id]:', error);
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Invalid request data', details: error.errors },
          { status: 400 }
        );
      }
      return NextResponse.json(
        { error: 'An unexpected error occurred' },
        { status: 500 }
      );
    }
  }
);

// DELETE /api/matters/[id] - Delete a matter
export const DELETE = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff],
  async (
    req: NextRequest,
    user: AuthUser,
    supabase: SupabaseClient<Database, "public", Database['public']>,
    context: Record<string, unknown>
  ): Promise<Response> => {
    try {
      const params = context.params as { id: string } | undefined;

      if (!params || !params.id) {
        return NextResponse.json({ error: 'No parameters provided' }, { status: 400 });
      }

      const { id } = params;

      if (!id || !z.string().uuid().safeParse(id).success) {
        return NextResponse.json({ error: 'Invalid matter ID' }, { status: 400 });
      }

      // Validate tenantId is defined
      if (!user.tenantId) {
        console.error('tenantId is undefined in JWT claims');
        return NextResponse.json(
          { error: 'Authorization error: Missing tenant information' },
          { status: 400 }
        );
      }

      // Create services instance
      const services = createServices(supabase, user.tenantId);

      try {
        // Delete matter using the service (this should handle matters, not cases)
        const success = await services.matters?.delete?.(id, user.id) || 
                       await services.cases?.delete?.(id, user.id); // Fallback for backward compatibility

        if (success) {
          return NextResponse.json({ success: true, message: 'Matter deleted successfully' });
        } else {
          return NextResponse.json({ error: 'Matter not found' }, { status: 404 });
        }
      } catch (error) {
        if (error instanceof Error && error.message === 'Matter not found') {
          return NextResponse.json({ error: 'Matter not found' }, { status: 404 });
        } else if (error instanceof Error && error.message.includes('Matter has associated tasks')) {
          return NextResponse.json({ error: 'Cannot delete matter with associated tasks' }, { status: 409 });
        }
        throw error;
      }

    } catch (error) {
      console.error('Error in DELETE /api/matters/[id]:', error);
      return NextResponse.json(
        { error: 'An unexpected error occurred' },
        { status: 500 }
      );
    }
  }
);
