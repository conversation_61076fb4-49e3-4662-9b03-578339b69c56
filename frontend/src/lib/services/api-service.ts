/**
 * API Service for PI Lawyer AI
 *
 * This service provides typed API access to the Python backend with proper
 * authentication handling for both user-facing and server-side operations.
 */

import { createClient } from '@supabase/supabase-js';
import { Document, DocumentSearchParams, DocumentSearchResponse } from '@/types/document';

// Environment variables (from .env.local)
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const API_SERVICE_TOKEN = process.env.API_SERVICE_TOKEN;
const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:8000';

// Initialize Supabase client
const supabase = SUPABASE_URL && SUPABASE_ANON_KEY
  ? createClient(SUPABASE_URL, SUPABASE_ANON_KEY)
  : null;

/**
 * API error class
 */
export class ApiError extends Error {
  status: number;

  constructor(message: string, status: number = 500) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
  }
}

/**
 * Get authentication token for API requests
 *
 * This will get the user's JWT token if available, or fall back to the service token
 * for server-side operations.
 *
 * @param forceServiceToken Force using the service token even if user token is available
 * @returns The authentication token or null if no authentication is available
 */
export async function getAuthToken(forceServiceToken: boolean = false): Promise<string | null> {
  // For server-side operations or when forced
  if (typeof window === 'undefined' || forceServiceToken) {
    return API_SERVICE_TOKEN ? `Bearer ${API_SERVICE_TOKEN}` : null;
  }

  // For client-side operations, try to get the user token
  if (!supabase) return null;

  const { data: { session } } = await supabase.auth.getSession();
  if (session?.access_token) {
    return `Bearer ${session.access_token}`;
  }

  return null;
}

/**
 * Base API client class
 */
export class ApiClient {
  /**
   * Make an authenticated API request
   *
   * @param endpoint API endpoint path (starting with /)
   * @param options Fetch options
   * @param useServiceToken Force using service token
   * @returns Response data
   * @throws ApiError if the request fails
   */
  protected async request<T>(
    endpoint: string,
    options: RequestInit = {},
    useServiceToken: boolean = false
  ): Promise<T> {
    const authToken = await getAuthToken(useServiceToken);

    const url = `${BACKEND_API_URL}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      ...(authToken ? { 'Authorization': authToken } : {}),
      ...options.headers,
    };

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      // Handle non-2xx responses
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ detail: 'Unknown error' }));
        throw new ApiError(
          errorData.detail || `API error: ${response.status} ${response.statusText}`,
          response.status
        );
      }

      // Parse JSON response
      const data = await response.json();
      return data as T;
    } catch (error) {
      if (error instanceof ApiError) throw error;

      // Wrap other errors
      throw new ApiError(
        error instanceof Error ? error.message : 'Unknown error',
        500
      );
    }
  }
}

/**
 * Documents API client
 */
export class DocumentsApi extends ApiClient {
  /**
   * Search for documents
   *
   * @param params Search parameters
   * @returns Search results
   */
  async searchDocuments(params: DocumentSearchParams): Promise<DocumentSearchResponse> {
    return this.request<DocumentSearchResponse>(
      '/api/document_search',
      {
        method: 'POST',
        body: JSON.stringify(params),
      }
    );
  }

  /**
   * Get a document by ID
   *
   * @param documentId Document ID
   * @returns Document
   */
  async getDocument(documentId: string): Promise<Document> {
    return this.request<Document>(`/api/documents/${documentId}`);
  }

  /**
   * Get a document by ID (server-side)
   *
   * This method uses the service token for authentication and is intended
   * for server-side operations. It accesses a separate endpoint that requires
   * service token authentication.
   *
   * @param documentId Document ID
   * @returns Document
   */
  async getDocumentServerSide(documentId: string): Promise<Document> {
    return this.request<Document>(
      `/api/service/documents/${documentId}`,
      {},
      true // Force service token
    );
  }
}

/**
 * Matters API client (formerly Cases API)
 */
export class MattersApi extends ApiClient {
  /**
   * Get all matters for the current user's firm
   *
   * @returns List of matters
   */
  async getMatters() {
    return this.request(`/api/matters`);
  }

  /**
   * Get a matter by ID
   *
   * @param matterId Matter ID
   * @returns Matter
   */
  async getMatter(matterId: string) {
    return this.request(`/api/matters/${matterId}`);
  }

  /**
   * Create a new matter
   *
   * @param matterData Matter creation data
   * @returns Created matter
   */
  async createMatter(matterData: any) {
    return this.request('/api/matters', {
      method: 'POST',
      body: JSON.stringify(matterData),
    });
  }

  /**
   * Update a matter
   *
   * @param matterId Matter ID
   * @param matterData Matter update data
   * @returns Updated matter
   */
  async updateMatter(matterId: string, matterData: any) {
    return this.request(`/api/matters/${matterId}`, {
      method: 'PUT',
      body: JSON.stringify(matterData),
    });
  }

  // Backward compatibility methods (deprecated)
  /** @deprecated Use getMatters() instead */
  async getCases() {
    return this.getMatters();
  }

  /** @deprecated Use getMatter() instead */
  async getCase(caseId: string) {
    return this.getMatter(caseId);
  }

  /** @deprecated Use createMatter() instead */
  async createCase(caseData: any) {
    return this.createMatter(caseData);
  }

  /** @deprecated Use updateMatter() instead */
  async updateCase(caseId: string, caseData: any) {
    return this.updateMatter(caseId, caseData);
  }
}

/**
 * Tasks API client
 */
export class TasksApi extends ApiClient {
  /**
   * Get all tasks for the current user's firm
   *
   * @returns List of tasks
   */
  async getTasks() {
    return this.request('/api/tasks');
  }

  /**
   * Get tasks for a specific matter
   *
   * @param matterId Matter ID
   * @returns List of tasks
   */
  async getTasksForMatter(matterId: string) {
    return this.request(`/api/matters/${matterId}/tasks`);
  }

  /** @deprecated Use getTasksForMatter() instead */
  async getTasksForCase(caseId: string) {
    return this.getTasksForMatter(caseId);
  }

  /**
   * Create a new task
   *
   * @param taskData Task creation data
   * @returns Created task
   */
  async createTask(taskData: any) {
    return this.request('/api/tasks', {
      method: 'POST',
      body: JSON.stringify(taskData),
    });
  }

  /**
   * Update a task's status
   *
   * @param taskId Task ID
   * @param status New status
   * @returns Updated task
   */
  async updateTaskStatus(taskId: string, status: string) {
    return this.request(`/api/tasks/${taskId}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status }),
    });
  }
}

/**
 * User API client
 */
export class UserApi extends ApiClient {
  /**
   * Get the current user's profile
   *
   * @returns User profile
   */
  async getCurrentUser() {
    return this.request('/api/me');
  }

  /**
   * Update the current user's profile
   *
   * @param profileData Profile update data
   * @returns Updated profile
   */
  async updateProfile(profileData: any) {
    return this.request('/api/me', {
      method: 'PUT',
      body: JSON.stringify(profileData),
    });
  }

  /**
   * Get all users in the current user's firm
   *
   * @param role Optional role filter
   * @returns List of users
   */
  async getFirmUsers(role?: string) {
    const queryParams = role ? `?role=${role}` : '';
    return this.request(`/api/firm/users${queryParams}`);
  }
}

// Create API clients
export const documentsApi = new DocumentsApi();
export const mattersApi = new MattersApi();
export const tasksApi = new TasksApi();
export const userApi = new UserApi();

// Backward compatibility
export const casesApi = mattersApi;

// Default export for convenience
export default {
  documents: documentsApi,
  matters: mattersApi,
  cases: mattersApi, // Backward compatibility
  tasks: tasksApi,
  user: userApi,
};
