/**
 * Practice Area Configuration
 * 
 * This file defines the hierarchical structure of practice areas,
 * combining work types (for system logic) with specific practice areas (for user experience).
 */

import { PracticeArea } from '@/types/domain/tenants/Matter'

export interface PracticeAreaOption {
  workType: PracticeArea
  practiceAreaCode: string
  label: string
  description: string
  icon: string
}

export interface PracticeAreaGroup {
  workType: PracticeArea
  label: string
  description: string
  icon: string
  options: PracticeAreaOption[]
}

/**
 * Comprehensive practice area configuration
 * Groups specific practice areas under work types for better UX
 */
export const PRACTICE_AREA_GROUPS: PracticeAreaGroup[] = [
  {
    workType: PracticeArea.LITIGATION,
    label: 'Litigation',
    description: 'Court proceedings, disputes, and trials',
    icon: 'Gavel',
    options: [
      {
        workType: PracticeArea.LITIGATION,
        practiceAreaCode: 'personal_injury',
        label: 'Personal Injury',
        description: 'Auto accidents, slip & fall, medical malpractice',
        icon: 'Heart'
      },
      {
        workType: PracticeArea.LITIGATION,
        practiceAreaCode: 'criminal_defense',
        label: 'Criminal Defense',
        description: 'Criminal charges, DUI, white collar crime',
        icon: 'Shield'
      },
      {
        workType: PracticeArea.LITIGATION,
        practiceAreaCode: 'family_law',
        label: 'Family Law',
        description: 'Divorce, custody, domestic relations',
        icon: 'Users'
      },
      {
        workType: PracticeArea.LITIGATION,
        practiceAreaCode: 'employment_law',
        label: 'Employment Law',
        description: 'Wrongful termination, discrimination, wage disputes',
        icon: 'Briefcase'
      },
      {
        workType: PracticeArea.LITIGATION,
        practiceAreaCode: 'civil_litigation',
        label: 'Civil Litigation',
        description: 'Contract disputes, business litigation, tort claims',
        icon: 'Scale'
      },
      {
        workType: PracticeArea.LITIGATION,
        practiceAreaCode: 'medical_malpractice',
        label: 'Medical Malpractice',
        description: 'Professional negligence, hospital liability',
        icon: 'Stethoscope'
      }
    ]
  },
  {
    workType: PracticeArea.TRANSACTION,
    label: 'Transactional',
    description: 'Business transactions and commercial deals',
    icon: 'Calculator',
    options: [
      {
        workType: PracticeArea.TRANSACTION,
        practiceAreaCode: 'real_estate',
        label: 'Real Estate',
        description: 'Property transactions, leasing, development',
        icon: 'Home'
      },
      {
        workType: PracticeArea.TRANSACTION,
        practiceAreaCode: 'corporate_law',
        label: 'Corporate Law',
        description: 'Business formation, M&A, governance',
        icon: 'Building'
      },
      {
        workType: PracticeArea.TRANSACTION,
        practiceAreaCode: 'contract_law',
        label: 'Contract Law',
        description: 'Contract drafting, review, negotiation',
        icon: 'FileText'
      },
      {
        workType: PracticeArea.TRANSACTION,
        practiceAreaCode: 'securities_law',
        label: 'Securities Law',
        description: 'Securities offerings, compliance, regulations',
        icon: 'TrendingUp'
      }
    ]
  },
  {
    workType: PracticeArea.ADVISORY,
    label: 'Advisory',
    description: 'Legal counsel and strategic advice',
    icon: 'Lightbulb',
    options: [
      {
        workType: PracticeArea.ADVISORY,
        practiceAreaCode: 'estate_planning',
        label: 'Estate Planning',
        description: 'Wills, trusts, probate administration',
        icon: 'FileCheck'
      },
      {
        workType: PracticeArea.ADVISORY,
        practiceAreaCode: 'tax_law',
        label: 'Tax Law',
        description: 'Tax planning, compliance, audits',
        icon: 'Calculator'
      },
      {
        workType: PracticeArea.ADVISORY,
        practiceAreaCode: 'immigration',
        label: 'Immigration',
        description: 'Visa applications, citizenship, deportation defense',
        icon: 'Globe'
      },
      {
        workType: PracticeArea.ADVISORY,
        practiceAreaCode: 'compliance',
        label: 'Compliance',
        description: 'Regulatory compliance, risk management',
        icon: 'CheckCircle'
      }
    ]
  },
  {
    workType: PracticeArea.IP,
    label: 'Intellectual Property',
    description: 'Patents, trademarks, and IP protection',
    icon: 'Lightbulb',
    options: [
      {
        workType: PracticeArea.IP,
        practiceAreaCode: 'patent_law',
        label: 'Patent Law',
        description: 'Patent prosecution, litigation, portfolio management',
        icon: 'Zap'
      },
      {
        workType: PracticeArea.IP,
        practiceAreaCode: 'trademark_law',
        label: 'Trademark Law',
        description: 'Trademark registration, enforcement, disputes',
        icon: 'Tag'
      },
      {
        workType: PracticeArea.IP,
        practiceAreaCode: 'copyright_law',
        label: 'Copyright Law',
        description: 'Copyright registration, licensing, infringement',
        icon: 'Copyright'
      },
      {
        workType: PracticeArea.IP,
        practiceAreaCode: 'trade_secrets',
        label: 'Trade Secrets',
        description: 'Trade secret protection, non-disclosure agreements',
        icon: 'Lock'
      }
    ]
  }
]

/**
 * Flattened list of all practice area options for easy lookup
 */
export const ALL_PRACTICE_AREA_OPTIONS: PracticeAreaOption[] = 
  PRACTICE_AREA_GROUPS.flatMap(group => group.options)

/**
 * Helper function to get practice area option by code
 */
export function getPracticeAreaOption(practiceAreaCode: string): PracticeAreaOption | undefined {
  return ALL_PRACTICE_AREA_OPTIONS.find(option => option.practiceAreaCode === practiceAreaCode)
}

/**
 * Helper function to get work type from practice area code
 */
export function getWorkTypeFromPracticeArea(practiceAreaCode: string): PracticeArea | undefined {
  const option = getPracticeAreaOption(practiceAreaCode)
  return option?.workType
}

/**
 * Helper function to get practice area options by work type
 */
export function getPracticeAreasByWorkType(workType: PracticeArea): PracticeAreaOption[] {
  const group = PRACTICE_AREA_GROUPS.find(g => g.workType === workType)
  return group?.options || []
}
