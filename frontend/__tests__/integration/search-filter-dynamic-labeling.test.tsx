/**
 * Integration tests for dynamic labeling in search and filter components
 * Tests that search interfaces display correct terminology based on context
 */

import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { vi } from "vitest";
import {
  PracticeArea,
  WorkType,
  MatterStatus,
  MatterPriorityLevel,
  Matter,
  getMatterDisplayLabel,
} from "@/types/domain/tenants/Matter";

// Mock the dynamic labeling function
vi.mock("@/types/domain/tenants/Matter", () => ({
  PracticeArea: {
    PERSONAL_INJURY: "personal_injury",
    CRIMINAL_DEFENSE: "criminal_defense",
    FAMILY_LAW: "family_law",
    REAL_ESTATE: "real_estate",
    BUSINESS_LAW: "business_law",
    EMPLOYMENT_LAW: "employment_law",
    IMMIGRATION: "immigration",
    INTELLECTUAL_PROPERTY: "intellectual_property",
    TAX_LAW: "tax_law",
    ESTATE_PLANNING: "estate_planning",
    BANKRUPTCY: "bankruptcy",
    ENVIRONMENTAL_LAW: "environmental_law",
    HEALTHCARE_LAW: "healthcare_law",
    SECURITIES_LAW: "securities_law",
    OTHER: "other",
  },
  WorkType: {
    LITIGATION: "litigation",
    TRANSACTIONAL: "transactional",
    ADVISORY: "advisory",
    ADR: "adr",
  },
  MatterStatus: {
    ACTIVE: "active",
    PENDING: "pending",
    CLOSED: "closed",
    REJECTED: "rejected",
    ON_HOLD: "on_hold",
  },
  MatterPriorityLevel: {
    LOW: "low",
    MEDIUM: "medium",
    HIGH: "high",
    URGENT: "urgent",
  },
  getMatterDisplayLabel: vi.fn(),
  getPracticeAreaDisplayName: vi.fn((area: string) => {
    const displayNames: Record<string, string> = {
      personal_injury: "Personal Injury",
      real_estate: "Real Estate",
      criminal_defense: "Criminal Defense",
      family_law: "Family Law",
      business_law: "Business Law",
      employment_law: "Employment Law",
      immigration: "Immigration",
      intellectual_property: "Intellectual Property",
      tax_law: "Tax Law",
      estate_planning: "Estate Planning",
      bankruptcy: "Bankruptcy",
      environmental_law: "Environmental Law",
      healthcare_law: "Healthcare Law",
      securities_law: "Securities Law",
      other: "Other",
    };
    return displayNames[area] || area;
  }),
  getWorkTypeDisplayName: vi.fn(),
  Matter: {}, // Mock the interface
}));

// Mock Next.js router
vi.mock("next/navigation", () => ({
  useRouter: () => ({
    push: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
  }),
  useSearchParams: () => ({
    get: vi.fn(),
    getAll: vi.fn(() => []),
  }),
  usePathname: () => "/matters",
}));

// Mock Supabase
vi.mock("@/lib/supabase/provider", () => ({
  useSupabase: () => ({
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          order: vi.fn(() => ({
            data: [
              {
                id: "1",
                title: "Personal Injury Case",
                practice_area: "personal_injury",
                work_type: "litigation",
                status: "active",
                created_at: "2024-01-01",
              },
              {
                id: "2",
                title: "Real Estate Matter",
                practice_area: "real_estate",
                work_type: "transactional",
                status: "active",
                created_at: "2024-01-02",
              },
            ],
            error: null,
          })),
        })),
      })),
    })),
  }),
}));

// Mock the matters API hook with complete function set
const mockMattersData: Matter[] = [
  {
    id: "1",
    tenantId: "tenant-1",
    title: "Personal Injury Case",
    description: "Car accident case",
    status: MatterStatus.ACTIVE,
    practiceArea: PracticeArea.PERSONAL_INJURY,
    workType: WorkType.LITIGATION,
    displayLabel: "Case",
    sensitive: false,
    caseNumber: "PI-2024-001",
    courtName: "Superior Court",
    jurisdiction: "State",
    filingDate: "2024-01-01",
    trialDate: "2024-06-01",
    primaryAttorneyId: "attorney-1",
    priorityLevel: MatterPriorityLevel.HIGH,
    statueOfLimitations: "2026-01-01",
    rejectionReason: null,
    createdBy: "user-1",
    createdAt: "2024-01-01T00:00:00Z",
    updatedBy: null,
    updatedAt: "2024-01-01T00:00:00Z",
  },
  {
    id: "2",
    tenantId: "tenant-1",
    title: "Real Estate Matter",
    description: "Property purchase",
    status: MatterStatus.ACTIVE,
    practiceArea: PracticeArea.REAL_ESTATE,
    workType: WorkType.TRANSACTIONAL,
    displayLabel: "Matter",
    sensitive: false,
    caseNumber: null,
    courtName: null,
    jurisdiction: null,
    filingDate: "2024-01-02",
    trialDate: null,
    primaryAttorneyId: "attorney-2",
    priorityLevel: MatterPriorityLevel.MEDIUM,
    statueOfLimitations: null,
    rejectionReason: null,
    createdBy: "user-1",
    createdAt: "2024-01-02T00:00:00Z",
    updatedBy: null,
    updatedAt: "2024-01-02T00:00:00Z",
  },
];

const mockStatsData = {
  total: 2,
  active: 2,
  upcomingDeadlines: 0,
  recentDocuments: 5,
};

// Create stable mock functions that we can reference
const mockGetAllMatters = vi.fn();
const mockGetMatterStats = vi.fn();
const mockGetMatterById = vi.fn();
const mockCreateMatter = vi.fn();
const mockUpdateMatter = vi.fn();
const mockDeleteMatter = vi.fn();
const mockGetMattersByPracticeArea = vi.fn();
const mockGetMattersByWorkType = vi.fn();
const mockGetLitigationMatters = vi.fn();
const mockSearchMatters = vi.fn();

const mockMattersApiReturn = {
  // Data and state
  isLoading: false,
  error: null,

  // CRUD operations - these return arrays/objects directly, not { data, error } objects
  getAllMatters: mockGetAllMatters,
  getMatterById: mockGetMatterById,
  createMatter: mockCreateMatter,
  updateMatter: mockUpdateMatter,
  deleteMatter: mockDeleteMatter,

  // Specialized operations - these also return arrays directly
  getMattersByPracticeArea: mockGetMattersByPracticeArea,
  getMattersByWorkType: mockGetMattersByWorkType,
  getLitigationMatters: mockGetLitigationMatters,
  getMatterStats: mockGetMatterStats,
  searchMatters: mockSearchMatters,
};

const mockUseMattersApi = vi.fn(() => mockMattersApiReturn);

vi.mock("@/hooks/useMattersApi", () => ({
  useMattersApi: mockUseMattersApi,
}));

// Mock the toast hook
vi.mock("@/hooks/use-toast", () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}));

// Mock useToast hook
vi.mock("@/components/ui/use-toast", () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}));

// Mock UI components that might cause issues
vi.mock("@/components/ui/button", () => ({
  Button: ({ children, ...props }: any) => (
    <button {...props}>{children}</button>
  ),
}));

vi.mock("@/components/ui/input", () => ({
  Input: (props: any) => <input {...props} />,
}));

vi.mock("@/components/ui/card", () => ({
  Card: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  CardContent: ({ children, ...props }: any) => (
    <div {...props}>{children}</div>
  ),
  CardHeader: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  CardTitle: ({ children, ...props }: any) => <h3 {...props}>{children}</h3>,
}));

vi.mock("@/components/ui/table", () => ({
  Table: ({ children, ...props }: any) => <table {...props}>{children}</table>,
  TableBody: ({ children, ...props }: any) => (
    <tbody {...props}>{children}</tbody>
  ),
  TableCell: ({ children, ...props }: any) => <td {...props}>{children}</td>,
  TableHead: ({ children, ...props }: any) => <th {...props}>{children}</th>,
  TableHeader: ({ children, ...props }: any) => (
    <thead {...props}>{children}</thead>
  ),
  TableRow: ({ children, ...props }: any) => <tr {...props}>{children}</tr>,
}));

vi.mock("@/components/ui/select", () => ({
  Select: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  SelectContent: ({ children, ...props }: any) => (
    <div {...props}>{children}</div>
  ),
  SelectItem: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  SelectTrigger: ({ children, ...props }: any) => (
    <button {...props}>{children}</button>
  ),
  SelectValue: ({ children, ...props }: any) => (
    <span {...props}>{children}</span>
  ),
}));

vi.mock("@/components/ui/badge", () => ({
  Badge: ({ children, ...props }: any) => <span {...props}>{children}</span>,
}));

vi.mock("@/components/ui/dropdown-menu", () => ({
  DropdownMenu: ({ children, ...props }: any) => (
    <div {...props}>{children}</div>
  ),
  DropdownMenuContent: ({ children, ...props }: any) => (
    <div {...props}>{children}</div>
  ),
  DropdownMenuItem: ({ children, ...props }: any) => (
    <div {...props}>{children}</div>
  ),
  DropdownMenuTrigger: ({ children, ...props }: any) => (
    <button {...props}>{children}</button>
  ),
  DropdownMenuSeparator: ({ ...props }: any) => <hr {...props} />,
}));

// Mock Lucide React icons
vi.mock("lucide-react", () => ({
  Search: () => <span>Search</span>,
  Filter: () => <span>Filter</span>,
  Plus: () => <span>Plus</span>,
  MoreHorizontal: () => <span>More</span>,
  Briefcase: () => <span>Briefcase</span>,
  FileText: () => <span>FileText</span>,
  Calendar: () => <span>Calendar</span>,
  AlertTriangle: () => <span>AlertTriangle</span>,
  Clock: () => <span>Clock</span>,
  User: () => <span>User</span>,
}));

describe("Search and Filter Dynamic Labeling Integration Tests", () => {
  const mockGetMatterDisplayLabel = getMatterDisplayLabel as ReturnType<
    typeof vi.fn
  >;

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup mock implementation
    mockGetMatterDisplayLabel.mockImplementation((workType: WorkType) => {
      return workType === WorkType.LITIGATION ? "Case" : "Matter";
    });

    // Reset all the mock functions to return the default data with debugging
    mockGetAllMatters.mockImplementation((filters?: any) => {
      console.log("mockGetAllMatters called with filters:", filters);

      // Apply filters to mock data
      let filteredData = [...mockMattersData];

      if (filters?.practiceArea && filters.practiceArea !== "all") {
        filteredData = filteredData.filter(
          (matter) => matter.practiceArea === filters.practiceArea,
        );
      }

      if (filters?.status && filters.status !== "all") {
        filteredData = filteredData.filter(
          (matter) => matter.status === filters.status,
        );
      }

      if (filters?.workType && filters.workType !== "all") {
        filteredData = filteredData.filter(
          (matter) => matter.workType === filters.workType,
        );
      }

      if (filters?.search) {
        filteredData = filteredData.filter(
          (matter) =>
            matter.title.toLowerCase().includes(filters.search.toLowerCase()) ||
            matter.description
              .toLowerCase()
              .includes(filters.search.toLowerCase()),
        );
      }

      return Promise.resolve(filteredData);
    });
    mockGetMatterStats.mockImplementation(() => {
      console.log("mockGetMatterStats called");
      return Promise.resolve(mockStatsData);
    });
    mockGetMatterById.mockResolvedValue(mockMattersData[0]);
    mockCreateMatter.mockResolvedValue(mockMattersData[0]);
    mockUpdateMatter.mockResolvedValue(mockMattersData[0]);
    mockDeleteMatter.mockResolvedValue({ success: true });
    mockGetMattersByPracticeArea.mockResolvedValue(mockMattersData);
    mockGetMattersByWorkType.mockResolvedValue(mockMattersData);
    mockGetLitigationMatters.mockResolvedValue([mockMattersData[0]]);
    mockSearchMatters.mockResolvedValue(mockMattersData);

    // Reset the main mock to return the default configuration
    mockUseMattersApi.mockReturnValue(mockMattersApiReturn);
  });

  describe("Matters List Page Search", () => {
    test("displays correct search placeholder based on mixed practice areas", async () => {
      const { default: MattersPage } = await import(
        "@/app/(authenticated)/matters/page"
      );

      render(<MattersPage />);

      // Wait for component to load
      await waitFor(() => {
        expect(screen.getByText(/matters/i)).toBeInTheDocument();
      });

      // Check search input placeholder
      const searchInput = screen.getByPlaceholderText(/search matters/i);
      expect(searchInput).toBeInTheDocument();
    });

    test("updates search results with correct terminology", async () => {
      // Override the mock to simulate loaded state
      vi.mocked(mockUseMattersApi).mockReturnValue({
        isLoading: false,
        error: null,
        getAllMatters: vi.fn(() => Promise.resolve(mockMattersData)),
        getMatterById: vi.fn(() => Promise.resolve(null)),
        createMatter: vi.fn(() => Promise.resolve(null)),
        updateMatter: vi.fn(() => Promise.resolve(null)),
        deleteMatter: vi.fn(() => Promise.resolve()),
        getMattersByPracticeArea: vi.fn(() => Promise.resolve([])),
        getMattersByWorkType: vi.fn(() => Promise.resolve([])),
        getLitigationMatters: vi.fn(() => Promise.resolve([])),
        getMatterStats: vi.fn(() => Promise.resolve(mockStatsData)),
        searchMatters: vi.fn(() => Promise.resolve([])),
      });

      const { default: MattersPage } = await import(
        "@/app/(authenticated)/matters/page"
      );

      render(<MattersPage />);

      // Wait for component to render with longer timeout
      await waitFor(
        () => {
          expect(
            screen.getByPlaceholderText(/search matters or clients/i),
          ).toBeInTheDocument();
        },
        { timeout: 10000 },
      ); // 10 second timeout

      // Verify data is displayed
      expect(screen.getByText(/Personal Injury Case/i)).toBeInTheDocument();
      expect(screen.getByText(/Real Estate Matter/i)).toBeInTheDocument();

      // Perform search
      const searchInput = screen.getByPlaceholderText(
        /search matters or clients/i,
      );
      fireEvent.change(searchInput, { target: { value: "Personal Injury" } });

      // Verify search results maintain correct terminology
      await waitFor(() => {
        expect(screen.getByText(/Personal Injury Case/i)).toBeInTheDocument();
      });
    });
  });

  describe("Filter Components", () => {
    test("practice area filter displays correct options", async () => {
      const { default: MattersPage } = await import(
        "@/app/(authenticated)/matters/page"
      );

      const { container } = render(<MattersPage />);

      // Wait for component to load
      await waitFor(() => {
        expect(screen.getByText(/Search and Filter/i)).toBeInTheDocument();
      });

      // Verify that practice area options exist in the DOM (they're rendered by default)
      // Use more specific selectors to target dropdown options, not table data
      const personalInjuryOption = container.querySelector(
        '[value="personal_injury"]',
      );
      const realEstateOption = container.querySelector('[value="real_estate"]');
      const allPracticeAreasOption = container.querySelector('[value="all"]');

      // Practice area options should exist in the DOM
      expect(personalInjuryOption).toBeTruthy();
      expect(realEstateOption).toBeTruthy();
      expect(allPracticeAreasOption).toBeTruthy();
    });

    test("status filter uses appropriate terminology", async () => {
      const { default: MattersPage } = await import(
        "@/app/(authenticated)/matters/page"
      );

      const { container } = render(<MattersPage />);

      // Wait for component to load
      await waitFor(() => {
        expect(screen.getByText(/Search and Filter/i)).toBeInTheDocument();
      });

      // Verify that status options exist in the DOM (they're rendered by default)
      // Use more specific selectors to target dropdown options
      const activeOption = container.querySelector('[value="active"]');
      const closedOption = container.querySelector('[value="closed"]');
      const allStatusOption = container.querySelector('[value="all"]');

      // Status options should exist in the DOM
      expect(activeOption).toBeTruthy();
      expect(closedOption).toBeTruthy();
      expect(allStatusOption).toBeTruthy();
    });

    test("filter results update table headers correctly", async () => {
      const { default: MattersPage } = await import(
        "@/app/(authenticated)/matters/page"
      );

      render(<MattersPage />);

      // Wait for component to load - look for table header "Matter" (dynamic label)
      await waitFor(
        () => {
          expect(
            screen.getByRole("columnheader", { name: /matter/i }),
          ).toBeInTheDocument();
        },
        { timeout: 10000 },
      );

      // Apply practice area filter for personal injury
      // Find the practice area select trigger by looking for the third button (after New Matter and two empty ones)
      const allButtons = screen.getAllByRole("button");
      const practiceAreaTrigger = allButtons[3]; // Third button should be practice area
      expect(practiceAreaTrigger).toBeInTheDocument();

      // Use userEvent to interact with the select
      const user = userEvent.setup();
      await user.click(practiceAreaTrigger);

      // Wait for dropdown to open and find the Personal Injury option
      await waitFor(async () => {
        const personalInjuryOption = screen.getByText("Personal Injury");
        expect(personalInjuryOption).toBeInTheDocument();
        await user.click(personalInjuryOption);
      });

      // Verify table shows only personal injury cases
      await waitFor(
        () => {
          expect(screen.getByText(/Personal Injury Case/i)).toBeInTheDocument();
          expect(
            screen.queryByText(/Real Estate Matter/i),
          ).not.toBeInTheDocument();
        },
        { timeout: 10000 },
      );
    });
  });

  describe("Advanced Search Features", () => {
    test("search functionality works with apply filters button", async () => {
      const { default: MattersPage } = await import(
        "@/app/(authenticated)/matters/page"
      );

      render(<MattersPage />);

      // Wait for component to load
      await waitFor(
        () => {
          expect(screen.getByText(/Apply Filters/i)).toBeInTheDocument();
        },
        { timeout: 10000 },
      );

      // Enter search query
      const searchInput = screen.getByPlaceholderText(
        /search matters or clients/i,
      );
      fireEvent.change(searchInput, { target: { value: "Personal Injury" } });

      // Wait for component to finish loading after search input change
      await waitFor(
        () => {
          expect(screen.getByText(/Apply Filters/i)).toBeInTheDocument();
        },
        { timeout: 10000 },
      );

      // Click apply filters button
      const applyButton = screen.getByText(/Apply Filters/i);
      fireEvent.click(applyButton);

      // Verify search results are maintained
      await waitFor(
        () => {
          expect(screen.getByText(/Personal Injury Case/i)).toBeInTheDocument();
        },
        { timeout: 10000 },
      );
    });

    test("clear filters functionality works correctly", async () => {
      const { default: MattersPage } = await import(
        "@/app/(authenticated)/matters/page"
      );

      render(<MattersPage />);

      // Wait for component to load
      await waitFor(() => {
        expect(screen.getByText(/Apply Filters/i)).toBeInTheDocument();
      });

      // Apply some filters
      const searchInput = screen.getByPlaceholderText(
        /search matters or clients/i,
      );

      const user = userEvent.setup();
      await user.type(searchInput, "t");

      // Click Apply Filters to trigger the search
      const applyButton = screen.getByText(/Apply Filters/i);
      await user.click(applyButton);

      // Wait for search to complete
      await waitFor(
        () => {
          expect(searchInput).toHaveValue("t");
        },
        { timeout: 3000 },
      );

      // Debug: Check if Clear filters button exists and search input state
      const clearButtons = screen.queryAllByText(/Clear filters/i);
      console.log("Clear filters buttons found:", clearButtons.length);
      console.log(
        "Search input value:",
        (searchInput as HTMLInputElement).value,
      );

      // The issue might be that the search input value is cleared but the API call still has the search
      // Let's check if we need to wait for the component state to update
      await waitFor(
        () => {
          // Check if the search input still has the value we typed
          expect(searchInput).toHaveValue("t");
        },
        { timeout: 3000 },
      );

      // Now look for Clear filters button - there might be multiple instances
      await waitFor(
        () => {
          const clearButton = screen.getByText(/Clear filters/i);
          expect(clearButton).toBeInTheDocument();
        },
        { timeout: 8000 },
      );

      // Click clear filters
      const clearButton = screen.getByText(/Clear filters/i);
      console.log("About to click Clear filters button");
      await user.click(clearButton);
      console.log("Clicked Clear filters button");

      // Wait a moment for the click to process
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Check if the API was called without search parameter (indicating clear worked)
      console.log(
        "Search input value after clear:",
        (searchInput as HTMLInputElement).value,
      );

      // Wait for filters to be cleared - check if Clear filters button disappears
      // This indicates that the searchQuery state was successfully cleared
      await waitFor(
        () => {
          const clearButtonsAfterClear =
            screen.queryAllByText(/Clear filters/i);
          expect(clearButtonsAfterClear).toHaveLength(0);
        },
        { timeout: 10000 },
      );

      // Verify that the API was called without search parameter (proving clear worked)
      // Check that one of the recent calls was without search parameter
      const recentCalls = mockGetAllMatters.mock.calls.slice(-3); // Last 3 calls
      const clearCall = recentCalls.find(
        (call) => call[0].page === 1 && call[0].limit === 10 && !call[0].search,
      );
      expect(clearCall).toBeDefined();
    }, 15000);
  });

  describe("Search Results Display", () => {
    test("matters data displays correctly in table", async () => {
      const { default: MattersPage } = await import(
        "@/app/(authenticated)/matters/page"
      );

      render(<MattersPage />);

      // Wait for component to load and show results
      await waitFor(() => {
        expect(screen.getByText(/Personal Injury Case/i)).toBeInTheDocument();
        expect(screen.getByText(/Real Estate Matter/i)).toBeInTheDocument();
      });

      // Verify both matters are displayed in the table
      expect(screen.getByText(/Personal Injury Case/i)).toBeInTheDocument();
      expect(screen.getByText(/Real Estate Matter/i)).toBeInTheDocument();
    });

    test("empty search results show appropriate message", async () => {
      const { default: MattersPage } = await import(
        "@/app/(authenticated)/matters/page"
      );

      // Mock empty results - override the existing mock to return empty array
      const { useMattersApi: mockUseMattersApi } = await import(
        "@/hooks/useMattersApi"
      );
      vi.mocked(mockUseMattersApi).mockReturnValue({
        isLoading: false,
        error: null,
        getAllMatters: vi.fn(() => Promise.resolve([])),
        getMatterById: vi.fn(() => Promise.resolve(null)),
        createMatter: vi.fn(() => Promise.resolve(null)),
        updateMatter: vi.fn(() => Promise.resolve(null)),
        deleteMatter: vi.fn(() => Promise.resolve({ success: true })),
        getMattersByPracticeArea: vi.fn(() => Promise.resolve([])),
        getMattersByWorkType: vi.fn(() => Promise.resolve([])),
        getLitigationMatters: vi.fn(() => Promise.resolve([])),
        getMatterStats: vi.fn(() =>
          Promise.resolve({
            total: 0,
            active: 0,
            upcomingDeadlines: 0,
            recentDocuments: 0,
          }),
        ),
        searchMatters: vi.fn(() => Promise.resolve([])),
      });

      render(<MattersPage />);

      // Wait for component to load - look for the actual empty state message
      await waitFor(() => {
        expect(screen.getByText(/No matters found/i)).toBeInTheDocument();
      });
    });
  });

  describe("Keyboard Navigation and Accessibility", () => {
    test("search input is keyboard accessible", async () => {
      const { default: MattersPage } = await import(
        "@/app/(authenticated)/matters/page"
      );

      render(<MattersPage />);

      // Wait for component to load
      await waitFor(() => {
        expect(screen.getByText(/Apply Filters/i)).toBeInTheDocument();
      });

      // Get search input after component has loaded
      const searchInput = screen.getByPlaceholderText(
        /search matters or clients/i,
      );
      expect(searchInput).toBeInTheDocument();

      // Test keyboard input functionality
      fireEvent.change(searchInput, { target: { value: "test search" } });
      expect(searchInput).toHaveValue("test search");

      // Test that Enter key can be pressed (keyboard accessibility)
      fireEvent.keyDown(searchInput, { key: "Enter" });

      // Wait for any potential re-renders and re-query the input
      await waitFor(() => {
        const updatedSearchInput = screen.getByPlaceholderText(
          /search matters or clients/i,
        );
        expect(updatedSearchInput).toBeInTheDocument();
        expect(updatedSearchInput).toHaveValue("test search");
      });
    });

    test("filter dropdowns are accessible via keyboard", async () => {
      const { default: MattersPage } = await import(
        "@/app/(authenticated)/matters/page"
      );

      const { container } = render(<MattersPage />);

      // Wait for component to load
      await waitFor(() => {
        const filterButtons = screen.getAllByRole("button");
        expect(filterButtons.length).toBeGreaterThan(0);
      });

      // Find the practice area select trigger button
      const practiceAreaTrigger = screen.getByText("Practice Area");

      // Test that the trigger is focusable (keyboard accessible)
      expect(practiceAreaTrigger).toBeInTheDocument();

      // Verify that practice area options exist in the DOM (they're rendered by default)
      // Use more specific selectors to target dropdown options, not table data
      const personalInjuryOption = container.querySelector(
        '[value="personal_injury"]',
      );
      const realEstateOption = container.querySelector('[value="real_estate"]');

      // At least one practice area option should exist
      expect(personalInjuryOption || realEstateOption).toBeTruthy();
    });
  });

  describe("Mobile Responsive Search", () => {
    test("search interface adapts to mobile viewport", async () => {
      // Mock mobile viewport
      Object.defineProperty(window, "innerWidth", {
        writable: true,
        configurable: true,
        value: 375,
      });

      const { default: MattersPage } = await import(
        "@/app/(authenticated)/matters/page"
      );

      render(<MattersPage />);

      // Wait for component to load
      await waitFor(() => {
        const searchInput = screen.getByPlaceholderText(
          /search matters or clients/i,
        );
        expect(searchInput).toBeInTheDocument();
      });

      // Verify search input is present and functional on mobile
      const searchInput = screen.getByPlaceholderText(
        /search matters or clients/i,
      );
      expect(searchInput).toBeInTheDocument();

      // Test that we can interact with the search input on mobile
      fireEvent.change(searchInput, { target: { value: "test" } });
      expect(searchInput).toHaveValue("test");
    });
  });
});
