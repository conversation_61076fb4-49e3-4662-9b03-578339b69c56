/**
 * Integration tests for dynamic labeling in search and filter components
 * Tests that search interfaces display correct terminology based on context
 */

import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { vi } from "vitest";
import {
  PracticeArea,
  WorkType,
  MatterStatus,
  MatterPriorityLevel,
  Matter,
  getMatterDisplayLabel,
} from "@/types/domain/tenants/Matter";

// Mock the dynamic labeling function
vi.mock("@/types/domain/tenants/Matter", () => ({
  PracticeArea: {
    PERSONAL_INJURY: "personal_injury",
    CRIMINAL_DEFENSE: "criminal_defense",
    FAMILY_LAW: "family_law",
    REAL_ESTATE: "real_estate",
    BUSINESS_LAW: "business_law",
    EMPLOYMENT_LAW: "employment_law",
    IMMIGRATION: "immigration",
    INTELLECTUAL_PROPERTY: "intellectual_property",
    TAX_LAW: "tax_law",
    ESTATE_PLANNING: "estate_planning",
    BANKRUPTCY: "bankruptcy",
    ENVIRONMENTAL_LAW: "environmental_law",
    HEALTHCARE_LAW: "healthcare_law",
    SECURITIES_LAW: "securities_law",
    OTHER: "other",
  },
  WorkType: {
    LITIGATION: "litigation",
    TRANSACTIONAL: "transactional",
    ADVISORY: "advisory",
    ADR: "adr",
  },
  MatterStatus: {
    ACTIVE: "active",
    PENDING: "pending",
    CLOSED: "closed",
    REJECTED: "rejected",
    ON_HOLD: "on_hold",
  },
  MatterPriorityLevel: {
    LOW: "low",
    MEDIUM: "medium",
    HIGH: "high",
    URGENT: "urgent",
  },
  getMatterDisplayLabel: vi.fn(),
  getPracticeAreaDisplayName: vi.fn((area: string) => {
    const displayNames: Record<string, string> = {
      personal_injury: "Personal Injury",
      real_estate: "Real Estate",
      criminal_defense: "Criminal Defense",
      family_law: "Family Law",
      business_law: "Business Law",
      employment_law: "Employment Law",
      immigration: "Immigration",
      intellectual_property: "Intellectual Property",
      tax_law: "Tax Law",
      estate_planning: "Estate Planning",
      bankruptcy: "Bankruptcy",
      environmental_law: "Environmental Law",
      healthcare_law: "Healthcare Law",
      securities_law: "Securities Law",
      other: "Other",
    };
    return displayNames[area] || area;
  }),
  getWorkTypeDisplayName: vi.fn(),
  Matter: {}, // Mock the interface
}));

// Mock Next.js router
vi.mock("next/navigation", () => ({
  useRouter: () => ({
    push: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
  }),
  useSearchParams: () => ({
    get: vi.fn(),
    getAll: vi.fn(() => []),
  }),
  usePathname: () => "/matters",
}));

// Mock Supabase
vi.mock("@/lib/supabase/provider", () => ({
  useSupabase: () => ({
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          order: vi.fn(() => ({
            data: [
              {
                id: "1",
                title: "Personal Injury Case",
                practice_area: "personal_injury",
                work_type: "litigation",
                status: "active",
                created_at: "2024-01-01",
              },
              {
                id: "2",
                title: "Real Estate Matter",
                practice_area: "real_estate",
                work_type: "transactional",
                status: "active",
                created_at: "2024-01-02",
              },
            ],
            error: null,
          })),
        })),
      })),
    })),
  }),
}));

// Mock the matters API hook with complete function set
const mockMattersData: Matter[] = [
  {
    id: "1",
    tenantId: "tenant-1",
    title: "Personal Injury Case",
    description: "Car accident case",
    status: MatterStatus.ACTIVE,
    practiceArea: PracticeArea.PERSONAL_INJURY,
    workType: WorkType.LITIGATION,
    displayLabel: "Case",
    sensitive: false,
    caseNumber: "PI-2024-001",
    courtName: "Superior Court",
    jurisdiction: "State",
    filingDate: "2024-01-01",
    trialDate: "2024-06-01",
    primaryAttorneyId: "attorney-1",
    priorityLevel: MatterPriorityLevel.HIGH,
    statueOfLimitations: "2026-01-01",
    rejectionReason: null,
    createdBy: "user-1",
    createdAt: "2024-01-01T00:00:00Z",
    updatedBy: null,
    updatedAt: "2024-01-01T00:00:00Z",
  },
  {
    id: "2",
    tenantId: "tenant-1",
    title: "Real Estate Matter",
    description: "Property purchase",
    status: MatterStatus.ACTIVE,
    practiceArea: PracticeArea.REAL_ESTATE,
    workType: WorkType.TRANSACTIONAL,
    displayLabel: "Matter",
    sensitive: false,
    caseNumber: null,
    courtName: null,
    jurisdiction: null,
    filingDate: "2024-01-02",
    trialDate: null,
    primaryAttorneyId: "attorney-2",
    priorityLevel: MatterPriorityLevel.MEDIUM,
    statueOfLimitations: null,
    rejectionReason: null,
    createdBy: "user-1",
    createdAt: "2024-01-02T00:00:00Z",
    updatedBy: null,
    updatedAt: "2024-01-02T00:00:00Z",
  },
];

const mockStatsData = {
  total: 2,
  active: 2,
  upcomingDeadlines: 0,
  recentDocuments: 5,
};

// Create stable mock functions that we can reference
const mockGetAllMatters = vi.fn();
const mockGetMatterStats = vi.fn();
const mockGetMatterById = vi.fn();
const mockCreateMatter = vi.fn();
const mockUpdateMatter = vi.fn();
const mockDeleteMatter = vi.fn();
const mockGetMattersByPracticeArea = vi.fn();
const mockGetMattersByWorkType = vi.fn();
const mockGetLitigationMatters = vi.fn();
const mockSearchMatters = vi.fn();

const mockMattersApiReturn = {
  // Data and state
  isLoading: false,
  error: null,

  // CRUD operations - these return arrays/objects directly, not { data, error } objects
  getAllMatters: mockGetAllMatters,
  getMatterById: mockGetMatterById,
  createMatter: mockCreateMatter,
  updateMatter: mockUpdateMatter,
  deleteMatter: mockDeleteMatter,

  // Specialized operations - these also return arrays directly
  getMattersByPracticeArea: mockGetMattersByPracticeArea,
  getMattersByWorkType: mockGetMattersByWorkType,
  getLitigationMatters: mockGetLitigationMatters,
  getMatterStats: mockGetMatterStats,
  searchMatters: mockSearchMatters,
};

const mockUseMattersApi = vi.fn(() => mockMattersApiReturn);

vi.mock("@/hooks/useMattersApi", () => ({
  useMattersApi: mockUseMattersApi,
}));

// Mock the toast hook
vi.mock("@/hooks/use-toast", () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}));

// Mock useToast hook
vi.mock("@/components/ui/use-toast", () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}));

// Mock UI components that might cause issues
vi.mock("@/components/ui/button", () => ({
  Button: ({ children, ...props }: any) => (
    <button {...props}>{children}</button>
  ),
}));

vi.mock("@/components/ui/input", () => ({
  Input: (props: any) => <input {...props} />,
}));

vi.mock("@/components/ui/card", () => ({
  Card: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  CardContent: ({ children, ...props }: any) => (
    <div {...props}>{children}</div>
  ),
  CardHeader: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  CardTitle: ({ children, ...props }: any) => <h3 {...props}>{children}</h3>,
}));

vi.mock("@/components/ui/table", () => ({
  Table: ({ children, ...props }: any) => <table {...props}>{children}</table>,
  TableBody: ({ children, ...props }: any) => (
    <tbody {...props}>{children}</tbody>
  ),
  TableCell: ({ children, ...props }: any) => <td {...props}>{children}</td>,
  TableHead: ({ children, ...props }: any) => <th {...props}>{children}</th>,
  TableHeader: ({ children, ...props }: any) => (
    <thead {...props}>{children}</thead>
  ),
  TableRow: ({ children, ...props }: any) => <tr {...props}>{children}</tr>,
}));

vi.mock("@/components/ui/select", () => ({
  Select: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  SelectContent: ({ children, ...props }: any) => (
    <div {...props}>{children}</div>
  ),
  SelectItem: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  SelectTrigger: ({ children, ...props }: any) => (
    <button {...props}>{children}</button>
  ),
  SelectValue: ({ children, ...props }: any) => (
    <span {...props}>{children}</span>
  ),
}));

vi.mock("@/components/ui/badge", () => ({
  Badge: ({ children, ...props }: any) => <span {...props}>{children}</span>,
}));

vi.mock("@/components/ui/dropdown-menu", () => ({
  DropdownMenu: ({ children, ...props }: any) => (
    <div {...props}>{children}</div>
  ),
  DropdownMenuContent: ({ children, ...props }: any) => (
    <div {...props}>{children}</div>
  ),
  DropdownMenuItem: ({ children, ...props }: any) => (
    <div {...props}>{children}</div>
  ),
  DropdownMenuTrigger: ({ children, ...props }: any) => (
    <button {...props}>{children}</button>
  ),
  DropdownMenuSeparator: ({ ...props }: any) => <hr {...props} />,
}));

// Mock Lucide React icons
vi.mock("lucide-react", () => ({
  Search: () => <span>Search</span>,
  Filter: () => <span>Filter</span>,
  Plus: () => <span>Plus</span>,
  MoreHorizontal: () => <span>More</span>,
  Briefcase: () => <span>Briefcase</span>,
  FileText: () => <span>FileText</span>,
  Calendar: () => <span>Calendar</span>,
  AlertTriangle: () => <span>AlertTriangle</span>,
  Clock: () => <span>Clock</span>,
  User: () => <span>User</span>,
}));

describe("Search and Filter Dynamic Labeling Integration Tests", () => {
  const mockGetMatterDisplayLabel = getMatterDisplayLabel as ReturnType<
    typeof vi.fn
  >;

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup mock implementation
    mockGetMatterDisplayLabel.mockImplementation((workType: WorkType) => {
      return workType === WorkType.LITIGATION ? "Case" : "Matter";
    });

    // Reset all the mock functions to return the default data with debugging
    mockGetAllMatters.mockImplementation((filters?: any) => {
      console.log("mockGetAllMatters called with filters:", filters);
      return Promise.resolve(mockMattersData);
    });
    mockGetMatterStats.mockImplementation(() => {
      console.log("mockGetMatterStats called");
      return Promise.resolve(mockStatsData);
    });
    mockGetMatterById.mockResolvedValue(mockMattersData[0]);
    mockCreateMatter.mockResolvedValue(mockMattersData[0]);
    mockUpdateMatter.mockResolvedValue(mockMattersData[0]);
    mockDeleteMatter.mockResolvedValue({ success: true });
    mockGetMattersByPracticeArea.mockResolvedValue(mockMattersData);
    mockGetMattersByWorkType.mockResolvedValue(mockMattersData);
    mockGetLitigationMatters.mockResolvedValue([mockMattersData[0]]);
    mockSearchMatters.mockResolvedValue(mockMattersData);

    // Reset the main mock to return the default configuration
    mockUseMattersApi.mockReturnValue(mockMattersApiReturn);
  });

  describe("Matters List Page Search", () => {
    test("displays correct search placeholder based on mixed practice areas", async () => {
      const { default: MattersPage } = await import(
        "@/app/(authenticated)/matters/page"
      );

      render(<MattersPage />);

      // Wait for component to load
      await waitFor(() => {
        expect(screen.getByText(/matters/i)).toBeInTheDocument();
      });

      // Check search input placeholder
      const searchInput = screen.getByPlaceholderText(/search matters/i);
      expect(searchInput).toBeInTheDocument();
    });

    test("updates search results with correct terminology", async () => {
      // Override the mock to simulate loaded state
      vi.mocked(mockUseMattersApi).mockReturnValue({
        isLoading: false,
        error: null,
        getAllMatters: vi.fn(() => Promise.resolve(mockMattersData)),
        getMatterById: vi.fn(() => Promise.resolve(null)),
        createMatter: vi.fn(() => Promise.resolve(null)),
        updateMatter: vi.fn(() => Promise.resolve(null)),
        deleteMatter: vi.fn(() => Promise.resolve()),
        getMattersByPracticeArea: vi.fn(() => Promise.resolve([])),
        getMattersByWorkType: vi.fn(() => Promise.resolve([])),
        getLitigationMatters: vi.fn(() => Promise.resolve([])),
        getMatterStats: vi.fn(() => Promise.resolve(mockStatsData)),
        searchMatters: vi.fn(() => Promise.resolve([])),
      });

      const { default: MattersPage } = await import(
        "@/app/(authenticated)/matters/page"
      );

      render(<MattersPage />);

      // Wait for component to render with longer timeout
      await waitFor(
        () => {
          expect(
            screen.getByPlaceholderText(/search matters or clients/i),
          ).toBeInTheDocument();
        },
        { timeout: 10000 },
      ); // 10 second timeout

      // Verify data is displayed
      expect(screen.getByText(/Personal Injury Case/i)).toBeInTheDocument();
      expect(screen.getByText(/Real Estate Matter/i)).toBeInTheDocument();

      // Perform search
      const searchInput = screen.getByPlaceholderText(
        /search matters or clients/i,
      );
      fireEvent.change(searchInput, { target: { value: "Personal Injury" } });

      // Verify search results maintain correct terminology
      await waitFor(() => {
        expect(screen.getByText(/Personal Injury Case/i)).toBeInTheDocument();
      });
    });
  });

  describe("Filter Components", () => {
    test("practice area filter displays correct options", async () => {
      const { default: MattersPage } = await import(
        "@/app/(authenticated)/matters/page"
      );

      render(<MattersPage />);

      // Wait for component to load
      await waitFor(() => {
        expect(screen.getByText(/Search and Filter/i)).toBeInTheDocument();
      });

      // Open practice area filter - it's a Select component with placeholder "Practice Area"
      const practiceAreaSelect = screen.getByDisplayValue("All Practice Areas");
      fireEvent.click(practiceAreaSelect);

      // Verify filter options are available
      await waitFor(() => {
        expect(screen.getByText(/Personal Injury/i)).toBeInTheDocument();
        expect(screen.getByText(/Real Estate/i)).toBeInTheDocument();
      });
    });

    test("status filter uses appropriate terminology", async () => {
      const { default: MattersPage } = await import(
        "@/app/(authenticated)/matters/page"
      );

      render(<MattersPage />);

      // Wait for component to load
      await waitFor(() => {
        expect(screen.getByText(/Search and Filter/i)).toBeInTheDocument();
      });

      // Open status filter - it's a Select component with default value "All Statuses"
      const statusSelect = screen.getByDisplayValue("All Statuses");
      fireEvent.click(statusSelect);

      // Verify status options are available
      await waitFor(() => {
        expect(screen.getByText(/Active/i)).toBeInTheDocument();
        expect(screen.getByText(/Closed/i)).toBeInTheDocument();
      });
    });

    test("filter results update table headers correctly", async () => {
      const { default: MattersPage } = await import(
        "@/app/(authenticated)/matters/page"
      );

      render(<MattersPage />);

      // Wait for component to load - look for table header "Matter" (dynamic label)
      await waitFor(
        () => {
          expect(
            screen.getByRole("columnheader", { name: /matter/i }),
          ).toBeInTheDocument();
        },
        { timeout: 10000 },
      );

      // Apply practice area filter for personal injury
      const practiceAreaSelect = screen.getByText("All Practice Areas");
      fireEvent.click(practiceAreaSelect);

      const personalInjuryOption = screen.getByText(/Personal Injury/i);
      fireEvent.click(personalInjuryOption);

      // Verify table shows only personal injury cases
      await waitFor(() => {
        expect(screen.getByText(/Personal Injury Case/i)).toBeInTheDocument();
        expect(
          screen.queryByText(/Real Estate Matter/i),
        ).not.toBeInTheDocument();
      });
    });
  });

  describe("Advanced Search Features", () => {
    test("search functionality works with apply filters button", async () => {
      const { default: MattersPage } = await import(
        "@/app/(authenticated)/matters/page"
      );

      render(<MattersPage />);

      // Wait for component to load
      await waitFor(
        () => {
          expect(screen.getByText(/Apply Filters/i)).toBeInTheDocument();
        },
        { timeout: 10000 },
      );

      // Enter search query
      const searchInput = screen.getByPlaceholderText(
        /search matters or clients/i,
      );
      fireEvent.change(searchInput, { target: { value: "Personal Injury" } });

      // Wait for component to finish loading after search input change
      await waitFor(
        () => {
          expect(screen.getByText(/Apply Filters/i)).toBeInTheDocument();
        },
        { timeout: 10000 },
      );

      // Click apply filters button
      const applyButton = screen.getByText(/Apply Filters/i);
      fireEvent.click(applyButton);

      // Verify search results are maintained
      await waitFor(
        () => {
          expect(screen.getByText(/Personal Injury Case/i)).toBeInTheDocument();
        },
        { timeout: 10000 },
      );
    });

    test("clear filters functionality works correctly", async () => {
      const { default: MattersPage } = await import(
        "@/app/(authenticated)/matters/page"
      );

      render(<MattersPage />);

      // Wait for component to load
      await waitFor(() => {
        expect(screen.getByText(/Apply Filters/i)).toBeInTheDocument();
      });

      // Apply some filters
      const searchInput = screen.getByPlaceholderText(
        /search matters or clients/i,
      );
      fireEvent.change(searchInput, { target: { value: "test" } });

      // Clear filters button should appear
      await waitFor(() => {
        expect(screen.getByText(/Clear filters/i)).toBeInTheDocument();
      });

      // Click clear filters
      const clearButton = screen.getByText(/Clear filters/i);
      fireEvent.click(clearButton);

      // Wait for filters to be cleared
      await waitFor(
        () => {
          expect(searchInput).toHaveValue("");
        },
        { timeout: 5000 },
      );
    });
  });

  describe("Search Results Display", () => {
    test("matters data displays correctly in table", async () => {
      const { default: MattersPage } = await import(
        "@/app/(authenticated)/matters/page"
      );

      render(<MattersPage />);

      // Wait for component to load and show results
      await waitFor(() => {
        expect(screen.getByText(/Personal Injury Case/i)).toBeInTheDocument();
        expect(screen.getByText(/Real Estate Matter/i)).toBeInTheDocument();
      });

      // Verify both matters are displayed in the table
      expect(screen.getByText(/Personal Injury Case/i)).toBeInTheDocument();
      expect(screen.getByText(/Real Estate Matter/i)).toBeInTheDocument();
    });

    test("empty search results show appropriate message", async () => {
      const { default: MattersPage } = await import(
        "@/app/(authenticated)/matters/page"
      );

      // Mock empty results - override the existing mock to return empty array
      const { useMattersApi: mockUseMattersApi } = await import(
        "@/hooks/useMattersApi"
      );
      vi.mocked(mockUseMattersApi).mockReturnValue({
        isLoading: false,
        error: null,
        getAllMatters: vi.fn(() => Promise.resolve([])),
        getMatterById: vi.fn(() => Promise.resolve(null)),
        createMatter: vi.fn(() => Promise.resolve(null)),
        updateMatter: vi.fn(() => Promise.resolve(null)),
        deleteMatter: vi.fn(() => Promise.resolve({ success: true })),
        getMattersByPracticeArea: vi.fn(() => Promise.resolve([])),
        getMattersByWorkType: vi.fn(() => Promise.resolve([])),
        getLitigationMatters: vi.fn(() => Promise.resolve([])),
        getMatterStats: vi.fn(() =>
          Promise.resolve({
            total: 0,
            active: 0,
            upcomingDeadlines: 0,
            recentDocuments: 0,
          }),
        ),
        searchMatters: vi.fn(() => Promise.resolve([])),
      });

      render(<MattersPage />);

      // Wait for component to load - look for the actual empty state message
      await waitFor(() => {
        expect(screen.getByText(/No matters found/i)).toBeInTheDocument();
      });
    });
  });

  describe("Keyboard Navigation and Accessibility", () => {
    test("search input is keyboard accessible", async () => {
      const { default: MattersPage } = await import(
        "@/app/(authenticated)/matters/page"
      );

      render(<MattersPage />);

      // Wait for component to load
      await waitFor(() => {
        const searchInput = screen.getByPlaceholderText(
          /search matters or clients/i,
        );
        expect(searchInput).toBeInTheDocument();
      });

      const searchInput = screen.getByPlaceholderText(
        /search matters or clients/i,
      );

      // Test keyboard input functionality
      fireEvent.change(searchInput, { target: { value: "test search" } });
      expect(searchInput).toHaveValue("test search");

      // Test that Enter key can be pressed (keyboard accessibility)
      fireEvent.keyDown(searchInput, { key: "Enter" });
      // Verify the input is still accessible and functional
      expect(searchInput).toBeInTheDocument();
      expect(searchInput).toHaveValue("test search");
    });

    test("filter dropdowns are accessible via keyboard", async () => {
      const { default: MattersPage } = await import(
        "@/app/(authenticated)/matters/page"
      );

      render(<MattersPage />);

      // Wait for component to load
      await waitFor(() => {
        const filterButtons = screen.getAllByRole("button");
        expect(filterButtons.length).toBeGreaterThan(0);
      });

      // Find the practice area select trigger button
      const practiceAreaSelect = screen.getByText("All Practice Areas");

      // Test keyboard interaction
      fireEvent.click(practiceAreaSelect);

      // Test that keyboard navigation works by pressing Enter
      fireEvent.keyDown(practiceAreaSelect, { key: "Enter" });

      // Verify dropdown opens by checking for options
      await waitFor(() => {
        expect(screen.getByText(/Personal Injury/i)).toBeInTheDocument();
      });
    });
  });

  describe("Mobile Responsive Search", () => {
    test("search interface adapts to mobile viewport", async () => {
      // Mock mobile viewport
      Object.defineProperty(window, "innerWidth", {
        writable: true,
        configurable: true,
        value: 375,
      });

      const { default: MattersPage } = await import(
        "@/app/(authenticated)/matters/page"
      );

      render(<MattersPage />);

      // Wait for component to load
      await waitFor(() => {
        const searchInput = screen.getByPlaceholderText(
          /search matters or clients/i,
        );
        expect(searchInput).toBeInTheDocument();
      });

      // Verify search input is present and functional on mobile
      const searchInput = screen.getByPlaceholderText(
        /search matters or clients/i,
      );
      expect(searchInput).toBeInTheDocument();

      // Test that we can interact with the search input on mobile
      fireEvent.change(searchInput, { target: { value: "test" } });
      expect(searchInput).toHaveValue("test");
    });
  });
});
